import { test, expect } from '@playwright/test'

// Contract:
// - Navigates to the given game property URL (must be running locally)
// - Enters 50000 into the price input
// - Clicks the "Submit Guess" button
// - Verifies that the result dialog appears and contains key markers
// - Takes a snapshot of relevant UI for regression detection

const FULL_URL =
  'http://localhost:9100/game/sydney-harbour-city-house-prices-game/property/89a91315-bce2-4110-b6eb-19d584b27482?session=session_1757334042579_t3mie6wx9'

// Locators chosen to be resilient against styling changes
const selectors = {
  // Prefer placeholder for Quasar QInput; label may not be associated to input
  guessPlaceholder: 'Enter your guess',
  submitButtonName: 'Submit Guess',
  feedbackDialogContains: 'Guess Result',
}

test.describe('Price Guess - submit flow', () => {
  test('enter 50000 and submit guess', async ({ page, baseURL }) => {
    // Go directly to the full URL to keep session semantics
    await page.goto(FULL_URL)

  // Ensure page structure is loaded (property content visible)
  await expect(page.locator('.property-content')).toBeVisible()

    // Fill in the guess value
  const input = page.getByPlaceholder(selectors.guessPlaceholder)
  await expect(input).toBeVisible()
  await input.fill('50000')

    // Click submit
  const submit = page.getByRole('button', { name: selectors.submitButtonName })
  await expect(submit).toBeEnabled()
  await submit.click()

    // Expect the result dialog to appear with the title "Guess Result"
  const dialog = page.locator('div.q-dialog')
  await expect(dialog.filter({ hasText: selectors.feedbackDialogContains })).toBeVisible()

    // Basic assertions on dialog content
  await expect(dialog).toContainText('Score')
  await expect(dialog).toContainText('Actual Price')
  })
})
