// Utilities to sanitize strings/objects for safe SSR/HTML embedding

/**
 * Sanitize a string by removing problematic control characters that can break
 * HTML parsers (keep TAB, LF, CR; strip others) and normalize JS line separators.
 *
 * @param {any} value
 * @returns {any}
 */
export function sanitizeString(value) {
  if (typeof value !== 'string') return value
  return (
    value
      // Remove C0 control chars (keep TAB, LF, and CR only)
      .replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F]/g, ' ')
      // Normalize Unicode line/para separators that can break JSON parsing
      .replace(/[\u2028\u2029]/g, ' ')
      // Remove any other problematic characters that might break HTML parsing
      .replace(/[\u007F-\u009F]/g, ' ')
  )
}

/**
 * Deeply sanitize all string values within an object/array.
 *
 * @template T
 * @param {T} input
 * @returns {T}
 */
export function deepSanitize(input) {
  if (Array.isArray(input)) {
    // @ts-ignore - preserve array typing loosely
    return input.map((v) => deepSanitize(v))
  }
  if (input && typeof input === 'object') {
    const out = Array.isArray(input) ? [] : {}
    for (const [k, v] of Object.entries(input)) {
      // @ts-ignore - dynamic assignment
      out[k] = deepSanitize(v)
    }
    // @ts-ignore
    return out
  }
  return sanitizeString(input)
}
