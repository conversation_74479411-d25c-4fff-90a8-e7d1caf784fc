// Extracted rBrandedGame route and its children from routes.js

export default [
  {
    // 13 june 2025 - price-game routes to replace price-guess
    // routes and use the new realty_game model on the back end
    // 22 june - change from price-game to game for hpg..com
    // as that uses diff backendpoint with game_default_locale as slug
    path: 'play/:gameSlug',
    name: 'rBrandedGame',
    component: () =>
      import('src/concerns/branded-game/layouts/BrandedGameLayout.vue'),
    meta: {
      title: 'Property Price Challenge - Interactive Real Estate Game',
      description:
        'Test your property market knowledge with our interactive price guessing game. Challenge yourself with real properties and see how well you know local values.',
      keywords:
        'property price game, real estate challenge, property valuation, house price quiz, market knowledge test',
      ogType: 'website',
      ogImage:
        'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
      twitterCard: 'summary_large_image',
    },
    children: [
      // {
      //   path: '',
      //   name: 'rBrandedGameStart',
      //   component: () =>
      //     import('src/concerns/realty-game/pages/RealtyGameStartPage.vue'),
      //   meta: {
      //     title: 'Start Property Price Challenge - Test Your Market Knowledge',
      //     description:
      //       'Ready to test your property market knowledge? Start our interactive price guessing game and see how well you know local property values.',
      //     keywords:
      //       'start property game, price challenge, real estate quiz, property knowledge test',
      //     ogType: 'website',
      //   },
      // },

      {
        path: '',
        name: 'rBrandedGameRootRedirect',
        redirect: { name: 'rSubdomainRoot' },
      },

      {
        path: 'withmap',
        name: 'rBrandedGameStartWithmap',
        component: () =>
          import('src/concerns/realty-game/pages/RealtyGameStartPage.vue'),
        meta: {
          title: 'Start Property Price Challenge - Test Your Market Knowledge',
          description:
            'Ready to test your property market knowledge? Start our interactive price guessing game and see how well you know local property values.',
          keywords:
            'start property game, price challenge, real estate quiz, property knowledge test',
          ogType: 'website',
        },
      },
      {
        path: 'superbee',
        name: 'rBrandedGamePropertiesAdmin',
        component: () =>
          import(
            'src/concerns/realty-game/pages/RealtyGamePropertiesAdmin.vue'
          ),
      },
      // {
      //   path: 'listing/:listingInGameUuid',
      //   name: 'rBrandedGameListing',
      //   component: () =>
      //     import('src/concerns/branded-game/pages/BrandedGameListingPage.vue'),
      // },
      {
        path: 'property/:listingInGameUuid',
        component: () =>
          import(
            'src/concerns/branded-game/layouts/BrandedGameListingLayout.vue'
          ),
        children: [
          {
            path: '',
            name: 'rBrandedGameProperty',
            component: () =>
              import(
                'src/concerns/branded-game/pages/BrandedGameListingPage.vue'
              ),
            props: (route) => ({
              listingInGameUuid: route.params.listingInGameUuid,
              routeSssnId: route.query.session,
            }),
            meta: {
              title: 'Guess Property Price - Property Price Challenge',
              description:
                "Make your best guess at this property's value. Test your knowledge of the local property market.",
              keywords:
                'property price guess, house valuation, property game, real estate quiz',
              ogType: 'website',
              robots: 'noindex, nofollow', // Game session specific
            },
          },
          {
            path: 'result/:routeSssnId',
            name: 'rBrandedGameSingleResult',
            component: () =>
              import(
                'src/concerns/branded-game/pages/BrandedGameResultsSummaryPage.vue'
              ),
            props: true,
            meta: {
              title: 'Your Property Price Challenge Results',
              description:
                'See how well you performed in the property price challenge. Share your results with friends!',
              keywords:
                'property game results, price guess results, property knowledge score',
              ogType: 'website',
              ogImage:
                'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
              robots: 'noindex, nofollow', // Game session specific
            },
          },
          {
            path: 'superbee',
            name: 'rBrandedGamePropertySuperbee',
            component: () =>
              import(
                'src/concerns/realty-game/pages/roundup/RoundupGamePropertyEditPage.vue'
              ),
            props: (route) => ({
              listingInGameUuid: route.params.listingInGameUuid,
              routeSssnId: route.query.session,
            }),
            meta: {
              robots: 'noindex, nofollow', // Game session specific
            },
          },
        ],
      },
      // {
      //   path: 'results_older/:routeSssnId',
      //   name: 'rBrandedGameResultsOlder',
      //   component: () =>
      //     import('src/concerns/realty-game/pages/RealtyGameResultsPage.vue'),
      // },
      {
        path: 'results/:routeSssnId',
        name: 'rBrandedGameResults',
        component: () =>
          import(
            'src/concerns/branded-game/pages/BrandedGameResultsSummaryPage.vue'
          ),
        props: true,
        meta: {
          title: 'Your Property Price Challenge Results',
          description:
            'See how well you performed in the property price challenge. Share your results with friends!',
          keywords:
            'property game results, price guess results, property knowledge score',
          ogType: 'website',
          ogImage:
            'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
          robots: 'noindex, nofollow', // Game session specific
        },
      },
      {
        path: 'results_to_share/:routeSssnId',
        name: 'rBrandedGameResultsShareable',
        component: () =>
          import(
            'src/concerns/realty-game/pages/RealtyGameResultsShareablePage.vue'
          ),
        props: true,
        meta: {
          title: 'Property Price Challenge Results - Shareable',
          description:
            'Property price challenge results without revealing actual prices. Perfect for sharing with friends!',
          keywords:
            'property game results, price guess results, shareable results, property knowledge',
          ogType: 'website',
          ogImage:
            'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
          robots: 'noindex, nofollow', // Game session specific
        },
      },
      {
        path: 'detailed_results/:routeSssnId',
        name: 'rBrandedGameResultsDetailed',
        component: () =>
          import(
            'src/concerns/realty-game/pages/RealtyGameResultsDetailedPage.vue'
          ),
        props: true,
        meta: {
          robots: 'noindex, nofollow', // Game session specific
        },
      },
      {
        path: 'shareable_detailed_results/:sessionUuid',
        name: 'rBrandedGameResultsDetailedByUuid',
        component: () =>
          import(
            'src/concerns/realty-game/pages/RealtyGameResultsDetailedPage.vue'
          ),
        props: true,
        meta: {
          robots: 'noindex, nofollow', // Game session specific
        },
      },
      {
        path: 'leaderboard',
        name: 'rBrandedGameLeaderboard',
        component: () =>
          import(
            'src/concerns/realty-game/pages/RealtyGameLeaderboardPage.vue'
          ),
        meta: {
          title: 'Property Price Challenge Leaderboard - Top Performers',
          description:
            'See the top performers in our property price challenge. Compare your scores with other property market enthusiasts.',
          keywords:
            'property game leaderboard, price guess rankings, property knowledge champions, real estate quiz scores',
          ogType: 'website',
          ogImage:
            'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
          twitterCard: 'summary_large_image',
        },
      },
    ],
  },
]
