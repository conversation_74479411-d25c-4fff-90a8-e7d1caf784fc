import { defineStore } from 'pinia'
import { sanitizeString } from 'src/utils/sanitize'

export const useRealtyGameMetaStore = defineStore('realtyGameMeta', {
  state: () => ({
    title: 'Property Price Challenge',
    description: 'Test your property market knowledge with our interactive price guessing game.',
    image: 'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
    url: 'https://housepriceguess.com',
    keywords: 'property price game, real estate challenge, property valuation, house price quiz, market knowledge test',
  }),
  actions: {
    setMeta({ title, description, image, url, keywords }) {
      if (title) this.title = sanitizeString(title)
      if (description) this.description = sanitizeString(description)
      if (image) this.image = sanitizeString(image)
      if (url) this.url = sanitizeString(url)
      if (keywords) this.keywords = sanitizeString(keywords)
    }
  }
})
