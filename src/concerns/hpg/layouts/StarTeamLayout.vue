<template>
  <q-layout view="lhh LpR ffr" class="hpg-main-layout-outer">
    <!-- <HtocGateMarketingHeader></HtocGateMarketingHeader> -->
    <q-header class="hpg-main-mht-ctr bg-white" reveal elevated>
      <q-toolbar
        style=""
        class="hpg-main-marketing-header-toolbar container max-ctr"
      >
        <q-toolbar-title class="inline-flex items-center">
          <!-- <div class="toolbar-site-label-main">
            <a class="ignore-link" href="https://housepriceguess.com/">
              <img
                class="hpg-logo-img"
                :src="logoUrl"
                alt="HousePriceGuess Logo"
              />
            </a>
          </div> -->

          <!-- Logo -->
          <a href="https://star.housepriceguess.com/" class="logo-link q-mr-sm">
            <img
              src="https://images.squarespace-cdn.com/content/v1/611ecc9714315f19333cb5e0/9895a9fe-0b9f-4bc8-b815-ff49fbb72df8/TheStarHomeTeam_75907490_BHGRE_StarHomes_Horizontal_WhiteonGreen_RGB01+%281%29.jpg?format=1500w"
              alt="House Price Guess Logo"
              class="site-logo hpg-logo-img"
              style="height: 45px; padding: 0px"
            />
          </a>

          <!-- Site Name -->
          <!-- <div class="toolbar-site-label-main">
            <a class="ignore-link" href="https://housepriceguess.com/">
              <div>
                <span class="color-second" style="color: #665df5">HOUSE</span>
                <span style="color: rgb(10, 0, 131)">PRICE</span>
                <span class="color-second" style="color: #665df5">GUESS</span>
              </div>
            </a>
          </div> -->
        </q-toolbar-title>
      </q-toolbar>
    </q-header>
    <q-page-container
      :class="[`main-layout-htoc-2024g-gate`, maxCtrClass]"
      style="padding-top: 50px"
    >
      <!-- must be careful not to add :key="$route.fullPath" below!! -->
      <router-view
        @blurbCta="blurbCta"
        :isPriceGuessOnly="true"
        @showNotification="showNotification"
        :whitelabelNameDisplay="whitelabelNameDisplay"
        :serviceEmail="serviceEmail"
        :gameTitle="gameTitle"
        :gameDesc="gameDesc"
        :gameListings="gameListings"
        :isLoading="isLoading"
        :gameDefaultCurrency="gameDefaultCurrency"
        :firstPropListing="firstPropListing"
        :totalProperties="totalProperties"
        :realtyGameSummary="realtyGameSummary"
        :gameCommunitiesDetails="gameCommunitiesDetails"
      />
    </q-page-container>
    <div class="ajax-bar-container">
      <q-ajax-bar
        ref="bar"
        position="top"
        color="accent"
        size="10px"
        :skip-hijack="false"
        :hijack-filter="loadingBarFilterFn"
      />
    </div>
    <StarTeamFooter
      homeUrl="https://housepriceguess.com/"
      :whitelabelNameDisplay="whitelabelNameDisplay"
      :serviceEmail="serviceEmail"
    ></StarTeamFooter>
    <!-- <HtocMobileFooter v-if="showMobileFooter"></HtocMobileFooter>
    <StarTeamFooter v-else></StarTeamFooter>
    <NewAccountEnquiryPrompt :showNewAccEnqPrompt="showNewAccEnqPrompt"
                             :selectedAccountPlan="selectedAccountPlan"
                             @blurbCtaEnd="blurbCtaEnd"></NewAccountEnquiryPrompt> -->
  </q-layout>
</template>

<script>
import StarTeamFooter from 'src/concerns/branded-game/components/head-foot/StarTeamFooter.vue'
//  'src/concerns/dossiers/components/head-foot/StarTeamFooter.vue'
import { defineComponent, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import { useRoute } from 'vue-router'
import useJsonLd from 'src/compose/useJsonLd.js'
import { useMeta } from 'quasar'
import { useRealtyGameMetaStore } from 'src/stores/realtyGameMeta'
import { storeToRefs } from 'pinia'
import { useBrandedGame } from 'src/concerns/realty-game/composables/useBrandedGame'

// Import logo image
import logoUrl from '/icons/favicon-128x128.png'

export default defineComponent({
  name: 'StarTeamLayout',
  components: {
    StarTeamFooter,
  },
  data() {
    return {
      showNewAccEnqPrompt: false,
      selectedAccountPlan: 'free',
    }
  },
  props: {
    serviceEmail: {
      type: String,
      default: '<EMAIL>',
    },
    whitelabelNameDisplay: {
      type: String,
      default: 'HousePriceGuess',
    },
  },
  computed: {
    showMobileFooter() {
      return this.$q.platform.is.mobile
    },
    maxCtrClass() {
      if (this.$route.name === 'rSubdomainRoot') {
        return ''
      } else {
        return 'max-ctr'
      }
    },
  },
  methods: {
    blurbCta(selectedAccountPlan) {
      this.selectedAccountPlan = selectedAccountPlan
      this.showNewAccEnqPrompt = true
    },
    blurbCtaEnd() {
      this.showNewAccEnqPrompt = false
    },
  },
  setup() {
    const $q = useQuasar()
    const route = useRoute()
    const brandedGameSlug = 'starhometeam-house-prices-game'

    // Initialize JSON-LD functionality
    const { initializeDefaultJsonLd, updateWebPageSchema, jsonLdScriptTags } =
      useJsonLd()

    // Initialize JSON-LD
    initializeDefaultJsonLd()

    // Update webpage schema based on route
    const routeMeta = route.meta || {}
    updateWebPageSchema({
      title: routeMeta.title,
      description: routeMeta.description,
      keywords: routeMeta.keywords,
    })

    // Meta management from store
    const metaStore = useRealtyGameMetaStore()
    const { title, description, image, url, keywords } = storeToRefs(metaStore)
    useMeta(() => ({
      title: title.value,
      meta: {
        description: { name: 'description', content: description.value },
        keywords: { name: 'keywords', content: keywords.value },
        'og:title': { property: 'og:title', content: title.value },
        'og:description': {
          property: 'og:description',
          content: description.value,
        },
        'og:image': { property: 'og:image', content: image.value },
        'og:url': { property: 'og:url', content: url.value },
        'og:type': { property: 'og:type', content: 'website' },
        'twitter:card': {
          name: 'twitter:card',
          content: 'summary_large_image',
        },
        'twitter:title': { name: 'twitter:title', content: title.value },
        'twitter:description': {
          name: 'twitter:description',
          content: description.value,
        },
        'twitter:image': { name: 'twitter:image', content: image.value },
        'twitter:url': { name: 'twitter:url', content: url.value },
      },
      link: {
        'preconnect-img': {
          rel: 'preconnect',
          href: 'https://images.unsplash.com',
          crossorigin: '',
        },
        'preconnect-api': {
          rel: 'preconnect',
          href: 'http://hpg-scoot.lvh.me:3333',
        },
        // Fonts preconnects if using Google Fonts
        'preconnect-fonts': {
          rel: 'preconnect',
          href: 'https://fonts.googleapis.com',
        },
        'preconnect-fonts-gstatic': {
          rel: 'preconnect',
          href: 'https://fonts.gstatic.com',
          crossorigin: '',
        },
      },
      script: jsonLdScriptTags.value.map((item) => ({
        type: 'application/ld+json',
        id: item.id,
        innerHTML: item.json,
      })),
    }))

    function showNotification(notificationMessage) {
      $q.notify(notificationMessage)
    }

    // ----- Branded Game Data Fetching (lifted from StarTeamHomePage2025) -----
    const {
      gameTitle,
      gameDesc,
      gameListings,
      isLoading,
      fetchBrandedGameData,
      isRedditRoute,
      gameDefaultCurrency,
      firstPropListing,
      totalProperties,
      realtyGameSummary,
      gameCommunitiesDetails,
    } = useBrandedGame()

    const loadBrandedGame = async () => {
      try {
        const isReddit = isRedditRoute(route.name)
        await fetchBrandedGameData(brandedGameSlug, isReddit)
      } catch (e) {
        $q.notify({
          color: 'negative',
          message: 'Failed to load property data',
          icon: 'error',
        })
      }
    }

    onMounted(() => {
      loadBrandedGame()
    })

    return {
      showNotification,
      loadingBarFilterFn(url) {
        return !(url.includes('prop_ev_init') || !url.includes('user'))
      },
      logoUrl, // Expose logoUrl to the template
      // branded game data exposed to child pages
      gameTitle,
      gameDesc,
      gameListings,
      isLoading,
      gameDefaultCurrency,
      firstPropListing,
      totalProperties,
      realtyGameSummary,
      gameCommunitiesDetails,
    }
  },
})
</script>

<style>
.main-layout-hpg-main-2024g {
  /* fix the edit tab disappearing */
  padding-top: 50px;
}
.hpg-logo-img {
  height: 40px; /* Smaller size to fit toolbar */
  width: auto;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  background: #fff;
  padding: 4px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.hpg-logo-img:hover {
  transform: scale(1.05) rotate(-2deg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
@media (max-width: 768px) {
  .hpg-logo-img {
    height: 32px; /* Slightly smaller for mobile */
    padding: 3px;
  }
}
.site-logo {
  height: 32px;
  width: auto;
  vertical-align: middle;
}

.logo-link {
  display: inline-block;
  margin-right: 12px;
}

.inline-flex {
  display: inline-flex;
  align-items: center;
}

.no-wrap {
  white-space: nowrap;
}
</style>
