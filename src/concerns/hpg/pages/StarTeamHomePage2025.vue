<template>
  <div class="star-team-game-layout">
    <!-- Header -->
    <div class="game-header">
      <div class="max-ctr">
        <div class="row items-center justify-between q-pa-md">
          <div class="header-left">
            <div>
              <h1 class="text-h4 text-weight-bold q-mb-sm">
                <router-link
                  style="color: #393; text-decoration: unset"
                  :to="{
                    name: 'rBrandedGameRootRedirect',
                    params: {
                      gameSlug: 'starhometeam-house-prices-game',
                    },
                  }"
                >
                  {{ gameTitle }}
                </router-link>
              </h1>
              <p class="text-h6 text-primary q-mb-lg">
                🏠 Guess the Price, Win the Pride! Test your skills on real
                homes in Northern Illinois & Southern Wisconsin. 🎉
              </p>
              <p class="text-body1 text-grey-7">
                {{ gameDesc }}
              </p>
              <p class="text-body1 text-grey-7">
                Now, The Star Home Team is introducing a fun and interactive way
                to engage with the housing market: a house price guessing game.
                This new feature lets you test your real estate instincts by
                guessing the list prices of actual homes, while learning more
                about the local market along the way. It’s a playful way to stay
                sharp, discover neighborhood trends, and see how close you are
                to thinking like a pro.
              </p>
            </div>
            <!-- <img
              class="hpg-logo-img q-mr-md"
              :src="logoUrl"
              alt="HousePriceGuess Logo"
            /> -->
          </div>
          <!-- <div class="header-right">
            <div class="row items-center q-gutter-sm">

              <div v-if="!!!isPriceGuessOnly">

              </div>
            </div>
          </div> -->
          <!---->
        </div>
      </div>
    </div>

    <div class="game-intro max-ctr">
      <!-- How it works -->
      <div id="how-it-works" class="how-it-works q-mb-xl">
        <div
          class="text-h5 text-weight-bold q-mt-lg q-mb-md text-center"
          style="color: #393"
        >
          How it works
        </div>
        <div class="steps-grid">
          <div class="step inmo-card-star">
            <q-icon name="search" color="primary" size="28px" />
            <div class="step-title">Pick a property</div>
            <div class="step-text">
              Browse real listings with photos and key details.
            </div>
          </div>
          <div class="step inmo-card-star">
            <q-icon name="visibility" color="primary" size="28px" />
            <div class="step-title">Review the clues</div>
            <div class="step-text">
              Check bedrooms, condition, and location signals.
            </div>
          </div>
          <div class="step inmo-card-star">
            <q-icon name="monetization_on" color="primary" size="28px" />
            <div class="step-title">Make your guess</div>
            <div class="step-text">
              Enter the price you think it sold for recently.
            </div>
          </div>
          <div class="step inmo-card-star">
            <q-icon name="insights" color="primary" size="28px" />
            <div class="step-title">See the result</div>
            <div class="step-text">
              Get instant feedback and improve every round.
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Main Content -->
    <div class="branded-game-content">
      <div
        class="text-h5 text-weight-bold q-mt-lg text-center"
        style="color: #393"
      >
        This week's properties
      </div>
      <div
        v-if="isLoading"
        class="loading-state q-my-xl column items-center justify-center"
      >
        <q-spinner size="50px" color="primary" />
        <div class="text-grey-7 q-mt-md">Loading properties…</div>
      </div>
      <div v-else class="q-pt-lg q-pb-sm">
        <div v-if="displayListings.length" class="branded-game-grid">
          <q-card
            v-for="listing in displayListings"
            :key="listing.uuid"
            class="game-listing-card hoverable inmo-card-star"
            flat
            bordered
            @click="goToListing(listing)"
          >
            <q-img
              :src="listing.imageUrl"
              :ratio="4 / 3"
              :alt="listing.title"
              loading="lazy"
              spinner-color="primary"
              style="margin-top: -2px"
            >
              <div
                v-if="listing.badges.length"
                class="absolute-top badges-row row no-wrap q-pa-sm q-gutter-xs"
              >
                <q-chip
                  v-for="b in listing.badges"
                  :key="b.label"
                  dense
                  square
                  color="primary"
                  text-color="white"
                  class="badge-chip"
                  >{{ b.label }}</q-chip
                >
              </div>
            </q-img>
            <q-card-section class="q-pb-none">
              <div class="text-subtitle1 text-weight-medium ellipsis-2-lines">
                {{ listing.title }}
              </div>
              <div
                v-if="listing.vicinity"
                class="text-caption text-grey-7 q-mt-xs ellipsis"
              >
                <q-icon name="place" size="14px" class="q-mr-xs" />
                {{ listing.vicinity }}
              </div>
            </q-card-section>
            <q-card-section class="q-pt-sm q-pb-sm">
              <div
                class="row items-center text-caption text-grey-8 q-gutter-sm"
              >
                <div v-if="listing.bedrooms !== null" class="row items-center">
                  <q-icon name="king_bed" size="16px" class="q-mr-xs" />
                  {{ listing.bedrooms }}
                </div>
                <div v-if="listing.bathrooms !== null" class="row items-center">
                  <q-icon name="bathtub" size="16px" class="q-mr-xs" />
                  {{ listing.bathrooms }}
                </div>
                <div v-if="listing.garages !== null" class="row items-center">
                  <q-icon name="garage" size="16px" class="q-mr-xs" />
                  {{ listing.garages }}
                </div>
              </div>
            </q-card-section>
            <q-separator inset />
            <q-card-actions align="between" class="q-px-sm q-pb-sm q-pt-xs">
              <q-btn
                color="primary"
                size="sm"
                flat
                label="Play"
                @click.stop="goToListing(listing)"
              />
              <q-btn
                icon="share"
                size="sm"
                flat
                round
                @click.stop="shareSingle(listing)"
              />
            </q-card-actions>
          </q-card>
        </div>
        <div v-else class="text-grey-7 q-pa-lg text-center">
          No listings available for this game yet.
        </div>
      </div>
    </div>

    <div class="game-extro max-ctr q-px-md">
      <div
        class="text-h5 text-weight-bold q-mb-lg text-center"
        style="color: #393"
      >
        Benefits of playing
      </div>
      <!-- Benefits / Value props -->
      <div class="benefits-card q-mb-xl" flat bordered>
        <div>
          <div class="benefits-grid">
            <div class="benefit inmo-card-star">
              <q-icon name="school" color="positive" size="28px" />
              <div>
                <div class="benefit-title">Learn the market</div>
                <div class="benefit-text">
                  Understand how features influence price.
                </div>
              </div>
            </div>
            <div class="benefit inmo-card-star">
              <q-icon name="trending_up" color="positive" size="28px" />
              <div>
                <div class="benefit-title">Spot trends</div>
                <div class="benefit-text">
                  See patterns across neighborhoods and time.
                </div>
              </div>
            </div>
            <div class="benefit inmo-card-star">
              <q-icon name="emoji_events" color="positive" size="28px" />
              <div>
                <div class="benefit-title">Friendly competition</div>
                <div class="benefit-text">
                  Share scores and challenge your circle.
                </div>
              </div>
            </div>
            <div class="benefit inmo-card-star">
              <q-icon name="lightbulb" color="positive" size="28px" />
              <div>
                <div class="benefit-title">Buyer/Seller confidence</div>
                <div class="benefit-text">
                  Build intuition for smarter decisions.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <q-card class="hero-card q-mb-xl" flat bordered>
        <q-card-section class="hero-content">
          <div class="hero-left">
            <div class="hero-kicker">New properties weekly</div>
            <h2 class="hero-title">Visit again soon for new challenges</h2>
            <p class="hero-subtitle" style="margin-bottom: 5px">
              Learn fast, have fun, and challenge your friends.
            </p>
            <p class="hero-subtitle">
              Come back often and see how your score improves.
            </p>
            <div class="hero-actions">
              <!-- <q-btn
                color="primary"
                unelevated
                size="lg"
                label="Start playing"
                :disable="isLoading || !displayListings.length"
                @click="startFirstListing"
              />
              <q-btn
                color="primary"
                outline
                size="lg"
                class="q-ml-sm"
                label="How it works"
                @click="scrollToSteps"
              /> -->
            </div>
          </div>
          <div class="hero-right">
            <div class="hero-bullets">
              <div class="bullet">
                <q-icon name="bolt" color="amber-7" /> Quick, casual rounds
              </div>
              <div class="bullet">
                <q-icon name="tips_and_updates" color="teal-6" /> Learn market
                signals
              </div>
              <div class="bullet">
                <q-icon name="groups" color="indigo-6" /> Compete & share
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>
      <!-- Weekly updates -->
      <!-- <q-banner class="updates-banner q-mb-xl bg-primary text-white" rounded>
        <template v-slot:avatar>
          <q-icon name="calendar_today" color="white" />
        </template>
        <div class="text-subtitle1 text-weight-medium">
          Fresh picks every week
        </div>
        <div class="text-body2 q-mt-xs">
          New properties are added weekly. Come back often and see how your
          score improves.
        </div>
        <q-btn
          class="q-ml-md"
          color="white"
          text-color="primary"
          label="Play this week's picks"
          @click="startFirstListing"
          flat
        />
      </q-banner> -->
    </div>
    <!-- <div v-if="gameTitle"
         class="game-content">
      <router-view :game-session-id="routeSssnId"
                   :current-property-index="currentPropertyIndex"
                   :gameTitle="gameTitle"
                   :gameDefaultCurrency="gameDefaultCurrency"
                   :gameCommunitiesDetails="gameCommunitiesDetails"
                   :realtyGameSummary="realtyGameSummary"
                   :firstPropListing="firstPropListing"
                   :totalProperties="totalProperties"
                   :shareableResultsUrl="shareableResultsUrl"
                   :isCurrentUserSession="isCurrentUserSession"
                   @update-progress="handleProgressUpdate"
                   @game-complete="handleGameComplete" />
    </div> -->

    <!-- Footer -->
    <div class="game-footer">
      <div class="max-ctr">
        <div class="row items-center justify-between q-pa-md">
          <div class="footer-left">
            <!-- <q-btn flat
                   color="grey-7"
                   icon="home"
                   label="Home" /> -->
          </div>
          <div>
            <SocialSharing
              socialSharingPrompt=""
              socialSharingTitle=""
              urlProp=""
            ></SocialSharing>
          </div>
          <!-- <div>
            <QrCodeShare urlProp=""
                           qrCodeTitle=""></QrCodeShare>
          </div> -->
          <div class="footer-right">
            <!-- <q-btn v-if="canShare"
                   flat
                   color="primary"
                   icon="share"
                   label="Share"
                   @click="shareGame" /> -->
          </div>
        </div>
      </div>
    </div>
    <!-- <CookieConsent /> -->
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
// import { useRealtyGameStore } from 'src/stores/realtyGame'
import useJsonLd from 'src/compose/useJsonLd.js'
import SocialSharing from 'src/concerns/dossiers/components/sharing/SocialSharing.vue'
// import logoUrl from '/icons/favicon-128x128.png'
//  'src/assets/hpg_logo_july_2025.png'
// // import CookieConsent from 'src/components/common/CookieConsent.vue'
// import QrCodeShare from "src/concerns/dossiers/components/sharing/QrCodeShare.vue"

// Define props
// This component receives these props from its parent (StarTeamLayout.vue)
const props = defineProps({
  isPriceGuessOnly: { type: Boolean, default: false },
  gameTitle: { type: [String, Object], default: '' },
  gameDesc: { type: [String, Object], default: '' },
  gameListings: { type: [Array, Object], default: () => [] },
  isLoading: { type: [Boolean, Object], default: false },
  gameDefaultCurrency: { type: [String, Object], default: 'USD' },
  firstPropListing: { type: [Object, null], default: null },
  totalProperties: { type: [Number, Object], default: 0 },
  realtyGameSummary: { type: [Object, null], default: null },
  gameCommunitiesDetails: { type: [Object, null], default: null },
})

// Create refs from props we actually use for reactivity
const { gameTitle, gameDesc, gameListings, isLoading } = toRefs(props)

const brandedGameSlug = 'starhometeam-house-prices-game'

const $router = useRouter()
const $q = useQuasar()

// Initialize JSON-LD functionality
const { initializeDefaultJsonLd, generateGameSessionSchema, addJsonLd } =
  useJsonLd()

// Placeholder for shareable links if needed in future

// UI state
// const showFooter = computed(() => {})
// const canShare = computed(() => {})

// Map raw listings to presentation-friendly objects
const displayListings = computed(() => {
  return gameListings.value.map((g, index) => {
    const ld = g.listing_details || {}
    // Support multiple possible field names
    const title =
      g.gl_title || ld.listing_title || ld.title || 'Untitled Property'
    const vicinity = g.gl_vicinity || ld.vicinity || ''
    const bedrooms = ld.listing_count_bedrooms ?? ld.count_bedrooms ?? null
    const bathrooms = ld.listing_count_bathrooms ?? ld.count_bathrooms ?? null
    const garages = ld.listing_count_garages ?? ld.count_garages ?? null
    const imageUrl =
      g.gl_image_url ||
      ld.main_image_url ||
      ld.image_url ||
      '/icons/favicon-128x128.png'
    return {
      uuid: g.uuid || ld.uuid,
      index,
      title,
      vicinity,
      bedrooms,
      bathrooms,
      garages,
      imageUrl,
      badges: [],
    }
  })
})

const goToListing = (realtyGameListing) => {
  $router.push({
    name: 'rBrandedGameProperty',
    params: {
      gameSlug: brandedGameSlug,
      listingInGameUuid: realtyGameListing.uuid,
    },
  })
}

const shareSingle = (listing) => {
  const url = `${location.origin}${
    $router.resolve({
      name: 'rBrandedGameProperty',
      params: { gameSlug: brandedGameSlug, propertyIndex: listing.index },
    }).href
  }`
  if (navigator.share) {
    navigator
      .share({ title: listing.title, url })
      .catch(() => copyToClipboard(url))
  } else {
    copyToClipboard(url)
  }
}

const copyToClipboard = (text) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
      $q.notify({
        message: 'Link copied',
        color: 'positive',
        icon: 'content_copy',
      })
    })
  }
}

// CTA helpers
const startFirstListing = () => {
  if (displayListings.value.length) {
    goToListing(displayListings.value[0])
  }
}
const scrollToSteps = () => {
  const el = document.getElementById('how-it-works')
  if (el) el.scrollIntoView({ behavior: 'smooth', block: 'start' })
}

// Initialize JSON-LD
initializeDefaultJsonLd()

// Add game-specific JSON-LD
const gameData = {
  title: gameTitle.value || 'Property Price Challenge',
  description:
    gameDesc.value ||
    'Test your property knowledge with our interactive price guessing game.',
  totalPlayers: 0, // This could be fetched from analytics
}
addJsonLd(generateGameSessionSchema(gameData), 'game-schema')

// Use the meta data with useMeta
// useMeta needs to be called AFTER JsonLd has been correctly created!!
// Only set meta tags if we're not on a property route (which handles its own meta tags)
// const isPropertyRoute = computed(() => false)

// const layoutMetaData = computed(() => {
//   // For property routes, don't set meta tags in layout - they're handled by preFetch
//   if (isPropertyRoute.value) {
//     // Only include JSON-LD scripts for property routes
//     return {
//       script: jsonLdMeta.value
//     }
//   }
//   // Otherwise return full meta data for non-property routes
//   return metaData.value
// })
</script>

<style scoped>
.star-team-game-layout {
  min-height: 100vh;
  /* background-color: #fafafa; */
  display: flex;
  flex-direction: column;
}

/* .max-ctr {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
} */

.game-header {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  /* position: sticky; */
  top: 0;
  z-index: 100;
}

.header-right {
  text-align: right;
}

.progress-info {
  margin-top: 0.5rem;
}

.game-content {
  flex: 1;
  overflow-y: auto;
}

.game-footer {
  background: white;
  border-top: 1px solid #e0e0e0;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

/* Branded game listings */
.branded-game-content {
  padding: 1.5rem 1rem 2.5rem;
  max-width: 1280px;
  margin: 0 auto;
}

.branded-game-grid {
  display: grid;
  /* Start with a single column and progressively enhance */
  grid-template-columns: 1fr;
  gap: 1.25rem;
}

.game-listing-card {
  cursor: pointer;
  transition: box-shadow 0.18s ease, transform 0.18s ease;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
}
.game-listing-card.hoverable:hover {
  box-shadow: 0 6px 18px -4px rgba(0, 0, 0, 0.18);
  transform: translateY(-4px);
}
.badges-row {
  background: linear-gradient(90deg, rgba(0, 0, 0, 0.55), rgba(0, 0, 0, 0.05));
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
.badge-chip {
  font-size: 10px;
  line-height: 1;
  padding: 2px 4px;
}

/* Utility */
.ellipsis-2-lines {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@media (max-width: 600px) {
  .branded-game-content {
    padding: 1rem 0.5rem 2rem;
  }
  .branded-game-grid {
    gap: 0.75rem;
  }
}

/* Ensure multiple columns on wider screens */
@media (min-width: 640px) {
  .branded-game-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 960px) {
  .branded-game-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
/* Up to 4 cards per row on very large screens */
@media (min-width: 1280px) {
  .branded-game-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .game-header .row {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-right {
    text-align: left;
    width: 100%;
  }

  .progress-info .q-linear-progress {
    width: 100% !important;
  }

  .hpg-logo-img {
    height: 40px;
    margin-bottom: 0.5rem;
  }

  .header-left {
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
  }
}

.hpg-logo-img {
  height: 60px;
  width: auto;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #fff;
  transition: transform 0.2s;
}

/* Intro (hero) */
.game-intro {
  /* max-width: 1200px; */
  margin: 0 auto;
  padding: 24px 16px 0;
}
.hero-card {
  border-radius: 14px;
  background: linear-gradient(180deg, #ffffff, #f7fbf8);
}
.hero-content {
  display: grid;
  grid-template-columns: 1.4fr 1fr;
  gap: 16px;
  align-items: center;
}
.hero-kicker {
  color: #3a933a;
  font-weight: 600;
  letter-spacing: 0.3px;
  margin-bottom: 6px;
}
.hero-title {
  font-size: 32px;
  line-height: 1.15;
  margin: 0 0 8px;
}
.hero-subtitle {
  color: #4a4a4a;
  margin: 0 0 16px;
}
.hero-actions {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.hero-right {
  display: flex;
  justify-content: center;
}
.hero-bullets {
  background: #fff;
  border: 1px solid #e8f2ea;
  border-radius: 12px;
  padding: 12px 14px;
  min-width: 260px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
}
.bullet {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 4px;
  font-weight: 500;
}

/* Steps */
.how-it-works {
  /* max-width: 1000px; */
  margin: 0 auto;
}
.steps-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 14px;
}
.step {
  background: #fff;
  border: 1px solid #eef3ef;
  border-radius: 12px;
  padding: 16px;
  text-align: left;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.04);
}
.step-title {
  font-weight: 600;
  margin-top: 8px;
}
.step-text {
  color: #5a5a5a;
  font-size: 14px;
  margin-top: 4px;
}

/* Benefits */
.benefits-card {
  background: #ffffff;
}
.benefits-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}
.benefit {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  border: 1px solid #eef3ef;
  border-radius: 12px;
  padding: 14px;
  background: #fff;
}
.benefit-title {
  font-weight: 600;
}
.benefit-text {
  color: #5a5a5a;
  font-size: 14px;
}

/* Updates banner */
.updates-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 16px;
}

@media (max-width: 960px) {
  .hero-content {
    grid-template-columns: 1fr;
  }
  .steps-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .benefits-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 600px) {
  .steps-grid {
    grid-template-columns: 1fr;
  }
  .benefits-grid {
    grid-template-columns: 1fr;
  }
  .hero-title {
    font-size: 26px;
  }
}

.hpg-logo-img:hover {
  transform: scale(1.05) rotate(-2deg);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.18);
}
</style>
