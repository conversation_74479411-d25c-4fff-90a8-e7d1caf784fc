import { ref, computed } from 'vue'
import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'
import { deepSanitize } from 'src/utils/sanitize'

// Unified handling for legacy and new API response shapes.
// Legacy shape (OLD):
// { realty_game_details: { ... }, price_guess_inputs: { game_listings: [], guessed_price_validation: {...}, game_communities_details: {...} } }
// New shape (NEW):
// { branded_game_summary: { realty_game_details: { ... }, game_listings: [], guessed_price_validation: {...}, game_communities_details: {...} } }
export function useBrandedGame() {
  // State
  const priceGuessData = ref(null)
  const isLoading = ref(false)
  const error = ref(null)
  const userGuesses = ref([])
  const gameResults = ref([])
  const validationRules = ref(null)

  // SSR adapter
  const setRealtyGameData = (data) => {
    if (!data?.properties) return
    priceGuessData.value = {
      branded_game_summary: {
        realty_game_details: {
          game_title: data.gameTitle || 'Property Price Challenge',
          game_description: data.gameDesc || '',
          game_bg_image_url: data.gameBgImageUrl || '',
          default_game_currency: data.gameDefaultCurrency || 'GBP',
        },
        game_listings: data.properties.map((p) => ({
          uuid: p.parentGameListingUuid,
          listing_details: { ...p },
        })),
        guessed_price_validation: {},
      },
    }
  }

  const normaliseResponse = (raw) => {
    if (!raw) return null
    // If already in new shape just return as-is
    if (raw?.branded_game_summary?.realty_game_details) return raw
    // Otherwise adapt legacy -> new
    return {
      branded_game_summary: {
        realty_game_details: raw.realty_game_details || {},
        game_listings:
          raw.price_guess_inputs?.game_listings ||
          raw.branded_game_summary?.game_listings ||
          [],
        guessed_price_validation:
          raw.price_guess_inputs?.guessed_price_validation ||
          raw.branded_game_summary?.guessed_price_validation ||
          {},
        game_communities_details:
          raw.price_guess_inputs?.game_communities_details ||
          raw.branded_game_summary?.game_communities_details ||
          {},
      },
      // Keep top-level legacy fields if any other code relies on them
      realty_game_details: raw.realty_game_details, // backward compat
    }
  }

  const extractValidationRules = () => {
    const summary = priceGuessData.value?.branded_game_summary
    validationRules.value = summary?.guessed_price_validation || null
  }

  const fetchBrandedGameData = async (gameSlug, isRedditRoute = false) => {
    isLoading.value = true
    error.value = null
    try {
      let apiUrl = isRedditRoute
        ? `${pwbFlexConfig.dataApiBase}/api_public/v4/realty_singular_game_summary`
        : `${pwbFlexConfig.dataApiBase}/api_public/v4/branded_game_summary`
      if (gameSlug) apiUrl += `/${gameSlug}`
      const { data } = await axios.get(apiUrl)
      priceGuessData.value = normaliseResponse(data)
      userGuesses.value = []
      gameResults.value = []
      extractValidationRules()
      return priceGuessData.value
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to fetch price guess data'
      console.error('Error fetching price guess data:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const fetchPropertyByUuid = async (propertyUuid) => {
    isLoading.value = true
    error.value = null
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/game_sale_listings/show_rgl/${propertyUuid}`
      const { data } = await axios.get(apiUrl)
      return data
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to fetch property details'
      console.error('Error fetching property details:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const fetchPriceGuessDataForAdmin = async (gameSlug) => {
    isLoading.value = true
    error.value = null
    try {
      let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/realty_game_inputs_for_admin`
      if (gameSlug) apiUrl += `/${gameSlug}`
      const { data } = await axios.get(apiUrl)
      priceGuessData.value = normaliseResponse(data)
      userGuesses.value = []
      gameResults.value = []
      extractValidationRules()
      return priceGuessData.value
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to fetch price guess data'
      console.error('Error fetching price guess data (admin):', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const savePriceEstimate = async (estimateData, realtyGameSlug) => {
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/branded_games/add_top_level_price_estimate/${realtyGameSlug}`
      const { data } = await axios.post(apiUrl, { price_estimate: estimateData })
      return data
    } catch (err) {
      console.error('Error saving price estimate:', err)
      throw err
    }
  }

  const fetchPriceEstimateComparisons = async (listingUuid) => {
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/price_estimates/comparisons/${listingUuid}`
      const { data } = await axios.get(apiUrl)
      return data
    } catch (err) {
      console.error('Error fetching price estimate comparisons:', err)
      throw err
    }
  }

  // Computed helpers
  const realtyGameDetails = computed(() => {
    return (
      priceGuessData.value?.branded_game_summary?.realty_game_details ||
      priceGuessData.value?.realty_game_details ||
      {}
    )
  })

  const gameListings = computed(() => {
    const listings =
      priceGuessData.value?.branded_game_summary?.game_listings ||
      priceGuessData.value?.price_guess_inputs?.game_listings ||
      []
    return listings.filter((g) => {
      const ld = g.listing_details || {}
      return ld.visible === true || ld.listing_visible === true
    })
  })

  const firstPropListing = computed(() =>
    gameListings.value.find((p) => {
      const ld = p.listing_details || {}
      return ld.visible === true || ld.listing_visible === true
    })
  )

  const adminProperties = computed(
    () =>
      priceGuessData.value?.branded_game_summary?.game_listings ||
      priceGuessData.value?.price_guess_inputs?.game_listings ||
      []
  )

  const realtyGameSummary = realtyGameDetails // alias
  const gameTitle = computed(() => realtyGameDetails.value?.game_title || '')
  const gameDefaultCurrency = computed(
    () => realtyGameDetails.value?.default_game_currency
  )
  const gameDesc = computed(
    () => realtyGameDetails.value?.game_description || ''
  )
  const gameCommunitiesDetails = computed(
    () =>
      priceGuessData.value?.branded_game_summary?.game_communities_details ||
      priceGuessData.value?.price_guess_inputs?.game_communities_details ||
      {}
  )
  const gameBgImageUrl = computed(
    () => realtyGameDetails.value?.game_bg_image_url || ''
  )
  const gameStartAt = computed(
    () => realtyGameDetails.value?.game_start_at || null
  )
  const gameEndAt = computed(() => realtyGameDetails.value?.game_end_at || null)
  const gameSlug = computed(
    () =>
      realtyGameDetails.value?.global_game_slug ||
      realtyGameDetails.value?.game_global_slug ||
      ''
  )
  const totalProperties = computed(() => gameListings.value.length)
  const totalScore = computed(() =>
    gameResults.value.reduce((sum, r) => sum + r.score, 0)
  )
  const maxPossibleScore = computed(() => totalProperties.value * 100)

  // Utilities
  const getPropertyByIndex = (i) => gameListings.value[i] || null
  const getPropertyByUuid = (uuid) =>
    gameListings.value.find(
      (p) => p.uuid === uuid || p.listing_details?.uuid === uuid
    )

  const formatPrice = (priceInCents, currency = 'GBP') => {
    if (!priceInCents) return 'Price not available'
    const amount = priceInCents / 100
    try {
      return new Intl.NumberFormat('en-UK', {
        style: 'currency',
        currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount)
    } catch {
      return `${currency} ${amount.toFixed(0)}`
    }
  }
  const getPropertyImages = (prop) => prop?.sale_listing_pics || []
  const getMainImage = (prop) => {
    const imgs = getPropertyImages(prop)
    return imgs[0] || null
  }

  const validateGuess = (guess, actualPrice) => {
    const errors = []
    if (!guess || guess <= 0) {
      errors.push(
        validationRules.value?.messages?.positive_number ||
          'Please enter a positive number'
      )
      return { isValid: false, errors }
    }
    const maxPct = validationRules.value?.max_percentage_above ?? 200
    const minPct = validationRules.value?.min_percentage_below ?? 90
    const maxAllowed = actualPrice * (1 + maxPct / 100)
    const minAllowed = actualPrice * (1 - minPct / 100)
    if (guess > maxAllowed) {
      errors.push(
        validationRules.value?.messages?.too_high ||
          `Guess is more than ${maxPct}% too high`
      )
    } else if (guess < minAllowed) {
      errors.push(
        validationRules.value?.messages?.too_low ||
          `Guess is more than ${minPct}% too low`
      )
    }
    return { isValid: errors.length === 0, errors }
  }

  const calculateScore = (guess, actual) => {
    const diff = Math.abs((guess - actual) / actual) * 100
    if (diff <= 5) return 100
    if (diff <= 10) return 90
    if (diff <= 15) return 80
    if (diff <= 20) return 70
    if (diff <= 25) return 60
    if (diff <= 35) return 50
    if (diff <= 50) return 40
    if (diff <= 75) return 30
    if (diff <= 100) return 20
    return 10
  }

  const getFeedbackMessage = (score) => {
    if (score >= 90) return 'Excellent! You have a great eye for property values.'
    if (score >= 80) return 'Very good! You were very close to the actual price.'
    if (score >= 70) return 'Good guess! You have a decent understanding of the market.'
    if (score >= 60) return 'Not too bad! You were reasonably close.'
    if (score >= 50) return "Fair attempt, but there's room for improvement."
    if (score >= 40) return 'Not terrible, but still quite a bit off.'
    if (score >= 30) return 'Quite far off, but keep practicing!'
    return 'Very far from the actual price. Study the market more!'
  }

  const submitGameGuess = async (guess, userInfo = {}, property = null, propertyIndex = 0) => {
    if (!property) return null
    const actualPrice = property.price_sale_current_cents / 100
    const guessAmount = parseFloat(guess)
    const validation = validateGuess(guessAmount, actualPrice)
    if (!validation.isValid) return { success: false, errors: validation.errors }
    const difference = ((guessAmount - actualPrice) / actualPrice) * 100
    const score = calculateScore(guessAmount, actualPrice)
    const feedback = getFeedbackMessage(score)
    const estimateData = {
      guessed_price_in_ui_currency_cents: Math.round(
        (userInfo.userGuessInUiCurrency || guessAmount) * 100
      ),
      ui_currency: userInfo.uiCurrency,
      score_for_guess: score,
      game_session_string: userInfo.sessionId || null,
      estimated_price_cents: Math.round(guessAmount * 100),
      price_at_time_of_estimate_cents: property.price_sale_current_cents,
      estimate_currency: property.currency || 'GBP',
      estimate_title: `${property.street_address} - ${property.title}`,
      estimate_text: `User guess: ${formatPrice(guessAmount * 100, property.currency)}`,
      estimate_vicinity: property.city,
      estimate_postal_code: property.postal_code,
      estimate_latitude_center: property.latitude,
      estimate_longitude_center: property.longitude,
      estimator_name: userInfo.name || 'Anonymous Player',
      is_for_sale_listing: true,
      is_for_rental_listing: false,
      percentage_above_or_below: difference,
      listing_uuid: property.uuid,
      user_uuid: userInfo.userUuid || null,
      scoot_uuid: userInfo.scootUuid || null,
      estimate_details: {
        game_score: score,
        property_index: propertyIndex,
        game_session_id: userInfo.sessionId || null,
        feedback_message: feedback,
      },
      game_session_id: userInfo.sessionId || null,
    }
    const result = {
      property,
      guess: guessAmount,
      actualPrice,
      difference,
      score,
      feedback,
      propertyIndex,
      estimateData,
    }
    try {
      const savedEstimate = await savePriceEstimate(estimateData, userInfo.realtyGameSlug)
      result.savedEstimate = savedEstimate
    } catch (e) {
      console.error('Failed to save estimate:', e)
      result.saveError = 'Failed to save estimate to server'
    }
    gameResults.value.push(result)
    userGuesses.value.push(guessAmount)
    return { success: true, result }
  }

  const resetGame = () => {
    userGuesses.value = []
    gameResults.value = []
  }
  const getScoreColor = (s) => {
    if (s >= 90) return 'positive'
    if (s >= 70) return 'primary'
    if (s >= 50) return 'info'
    if (s >= 30) return 'warning'
    return 'negative'
  }
  const getPerformanceRating = (total, max) => {
    const pct = (total / max) * 100
    if (pct >= 90) return { rating: 'Expert', color: 'positive', icon: 'star' }
    if (pct >= 80) return { rating: 'Advanced', color: 'positive', icon: 'trending_up' }
    if (pct >= 70) return { rating: 'Good', color: 'grey', icon: 'thumb_up' }
    if (pct >= 60) return { rating: 'Fair', color: 'grey', icon: 'thumbs_up_down' }
    if (pct >= 50) return { rating: 'Beginner', color: 'warning', icon: 'school' }
    return { rating: 'Novice', color: 'warning', icon: 'help' }
  }

  const fetchAllEstimatesForProperties = async () => {
    const all = []
    for (const property of gameListings.value) {
      const propertyUuid = property.listing_details_uuid || property.listing_details?.uuid
      try {
        const estimates = await fetchPriceEstimateComparisons(propertyUuid)
        all.push({ property, estimates })
      } catch (e) {
        console.error(`Failed to fetch estimates for property ${propertyUuid}:`, e)
        all.push({ property, estimates: [], error: e.message })
      }
    }
    return all
  }

  const updateGameListingVisibility = async (uuidForGame, propertyUuid, isVisible) => {
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_mgmt/v4/scoot_games_mgmt/${uuidForGame}/listing_in_game_visibility/${propertyUuid}`
      const { data } = await axios.patch(apiUrl, { sale_listing: { visible: isVisible } })
      return data
    } catch (err) {
      console.error('Error updating property visibility:', err)
      throw err
    }
  }
  const updatePropertyTitle = async (propertyUuid, title) => {
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_mgmt/v4/sale_listings/${propertyUuid}`
      const { data } = await axios.patch(apiUrl, { sale_listing: { title } })
      return data
    } catch (err) {
      console.error('Error updating property title:', err)
      throw err
    }
  }
  const updateImageVisibility = async (imageUuid, isHidden) => {
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_mgmt/v4/realty_games_mgmt/photo_visibility/${imageUuid}`
      const { data } = await axios.patch(apiUrl, { sale_listing_pic: { flag_is_hidden: isHidden } })
      return data
    } catch (err) {
      console.error('Error updating image visibility:', err)
      throw err
    }
  }
  const updateRealtyGameListingAttribute = async (realtyGameListingUuid, attribute, value) => {
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_mgmt/v4/scoot_games_mgmt/game_listing_attributes/${realtyGameListingUuid}`
      const { data } = await axios.patch(apiUrl, { realty_game_listing: { [attribute]: value } })
      return data
    } catch (err) {
      console.error(`Error updating realty game listing attribute ${attribute}:`, err)
      throw err
    }
  }
  const createPriceGuessGameMgmt = async (gameData) => {
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_mgmt/v4/realty_games_mgmt/create_game`
      const { data } = await axios.post(apiUrl, { price_guess_game: gameData })
      return data
    } catch (err) {
      console.error('Error creating price guess game:', err)
      throw err
    }
  }
  const createRealtyGameByUser = async (gameData) => {
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/forms_hpg/sp/create_game`
      const { data } = await axios.post(apiUrl, { price_guess_game: gameData })
      return data
    } catch (err) {
      console.error('Error creating price guess game:', err)
      throw err
    }
  }

  const fetchAndSetGameData = async (gameSlug, store, isRedditRoute = false) => {
    let apiUrl = isRedditRoute
      ? `${pwbFlexConfig.dataApiBase}/api_public/v4/realty_singular_game_summary/${gameSlug}`
      : `${pwbFlexConfig.dataApiBase}/api_public/v4/branded_game_summary/${gameSlug}`
    const { data } = await axios.get(apiUrl)
    const normalised = normaliseResponse(data)
    if (normalised) {
      priceGuessData.value = normalised
      extractValidationRules()
      const listings = gameListings.value
      const rgd = realtyGameDetails.value
      const storeData = {
        gameListings: deepSanitize(listings),
        gameTitle: deepSanitize(rgd?.game_title) || 'Property Price Challenge',
        gameDesc: deepSanitize(rgd?.game_description) || '',
        gameBgImageUrl: deepSanitize(rgd?.game_bg_image_url) || '',
        gameDefaultCurrency: rgd?.default_game_currency || 'GBP',
        totalProperties: listings.length,
        currentProperty: null,
        isDataLoaded: true,
      }
      store.setRealtyGameData(storeData)
      return storeData
    }
    return null
  }
  const isRedditRoute = (routeName) => routeName && routeName.startsWith('rReddit')
  const fetchAndSetGameDataAuto = async (slug, store, currentRoute = null) => {
    const isReddit = currentRoute ? isRedditRoute(currentRoute.name) : false
    return fetchAndSetGameData(slug, store, isReddit)
  }

  return {
    // state
    priceGuessData,
    isLoading,
    error,
    userGuesses,
    gameResults,
    // computed
    gameListings,
    firstPropListing,
    adminProperties,
    realtyGameSummary,
    gameTitle,
    gameDefaultCurrency,
    gameDesc,
    gameCommunitiesDetails,
    gameBgImageUrl,
    gameStartAt,
    gameEndAt,
    gameSlug,
    totalProperties,
    totalScore,
    maxPossibleScore,
    // methods
    fetchBrandedGameData,
    fetchPriceGuessDataForAdmin,
    fetchPropertyByUuid,
    savePriceEstimate,
    fetchPriceEstimateComparisons,
    fetchAllEstimatesForProperties,
    getPropertyByIndex,
    getPropertyByUuid,
    formatPrice,
    getPropertyImages,
    getMainImage,
    validateGuess,
    submitGameGuess,
    resetGame,
    getScoreColor,
    getPerformanceRating,
    updateGameListingVisibility,
    updatePropertyTitle,
    updateImageVisibility,
    updateRealtyGameListingAttribute,
    createPriceGuessGameMgmt,
    createRealtyGameByUser,
    setRealtyGameData,
    fetchAndSetGameData,
    fetchAndSetGameDataAuto,
    isRedditRoute,
  }
}
