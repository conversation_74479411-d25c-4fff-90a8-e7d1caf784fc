<template>
  <div class="game-creation-form">
    <q-form @submit="handleSubmit" class="q-gutter-md">
      <!-- Game Title -->
      <q-card class="modern-card q-pa-none gcf-card">
        <q-card-section>
          <div class="section-header q-mb-md">
            <q-icon name="edit" color="primary" size="md" class="q-mr-sm" />
            <h6 class="q-mt-none q-mb-none text-h6 text-weight-bold">Game Information</h6>
          </div>

          <q-input v-model="form.title"
                   label="Game Title *"
                   outlined
                   :rules="[val => !!val || 'Title is required']"
                   hint="Enter a catchy title for your price guessing game"
                   class="modern-input" />

          <q-input v-model="form.description"
                   label="Game Description"
                   type="textarea"
                   outlined
                   rows="3"
                   class="q-mt-md modern-input"
                   hint="Provide a brief description of the game (optional)" />
        </q-card-section>
      </q-card>

      <!-- Game Schedule -->
      <!-- <q-card class="modern-card q-pa-lg">
        <q-card-section>
          <div class="section-header q-mb-md">
            <q-icon name="schedule" color="primary" size="md" class="q-mr-sm" />
            <h6 class="q-mt-none q-mb-none text-h6 text-weight-bold">Game Schedule</h6>
          </div>

          <div class="row q-gutter-md">
            <div class="col-12 col-md-6">
              <q-input v-model="form.startDate"
                       label="Start Date *"
                       type="date"
                       outlined
                       class="modern-input"
                       :rules="[val => !!val || 'Start date is required']" />
            </div>
            <div class="col-12 col-md-6">
              <q-input v-model="form.startTime"
                       label="Start Time *"
                       type="time"
                       outlined
                       class="modern-input"
                       :rules="[val => !!val || 'Start time is required']" />
            </div>
          </div>

          <div class="row q-gutter-md q-mt-sm">
            <div class="col-12 col-md-6">
              <q-input v-model="form.endDate"
                       label="End Date *"
                       type="date"
                       outlined
                       class="modern-input"
                       :rules="[val => !!val || 'End date is required', validateEndDate]" />
            </div>
            <div class="col-12 col-md-6">
              <q-input v-model="form.endTime"
                       label="End Time *"
                       type="time"
                       outlined
                       class="modern-input"
                       :rules="[val => !!val || 'End time is required']" />
            </div>
          </div>

          <div class="q-mt-md">
            <q-banner v-if="scheduleSummary"
                      class="modern-banner bg-blue-1 text-blue-8">
              <template v-slot:avatar>
                <q-icon name="schedule" />
              </template>
              {{ scheduleSummary }}
            </q-banner>
          </div>
        </q-card-section>
      </q-card> -->

      <!-- Game Settings -->
      <!-- <q-card class="modern-card q-pa-lg">
        <q-card-section>
          <div class="section-header q-mb-md">
            <q-icon name="settings" color="primary" size="md" class="q-mr-sm" />
            <h6 class="q-mt-none q-mb-none text-h6 text-weight-bold">Game Settings</h6>
          </div>

          <q-toggle v-model="form.isActive"
                    label="Activate game immediately"
                    color="positive"
                    class="q-mb-md modern-toggle" />

          <q-toggle v-model="form.allowAnonymous"
                    label="Allow anonymous players"
                    color="primary"
                    class="q-mb-md modern-toggle" />

          <q-input v-model.number="form.maxPlayers"
                   label="Maximum Players (optional)"
                   type="number"
                   outlined
                   min="1"
                   class="modern-input"
                   hint="Leave empty for unlimited players" />
        </q-card-section>
      </q-card> -->

      <!-- Action Buttons -->
      <div class="row justify-center q-gutter-md q-mt-xl">
        <!-- <q-btn flat
               color="grey-7"
               label="Cancel"
               size="lg"
               class="cta-secondary"
               @click="$emit('cancel')" /> -->
        <q-btn type="submit"
               color="primary"
               label="Create Game"
               icon="add"
               size="lg"
               class="cta-primary"
               :loading="loading"
               :disable="!isFormValid" />
      </div>
    </q-form>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  initialData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['submit', 'cancel'])

// Form data
const form = ref({
  title: '',
  description: '',
  startDate: '',
  startTime: '',
  endDate: '',
  endTime: '',
  isActive: false,
  allowAnonymous: true,
  maxPlayers: null,
  ...props.initialData
})

// Computed properties
const isFormValid = computed(() => {
  return form.value.title &&
    form.value.startDate &&
    form.value.startTime &&
    form.value.endDate &&
    form.value.endTime
})

const scheduleSummary = computed(() => {
  if (!form.value.startDate || !form.value.endDate) return ''

  const startDateTime = new Date(`${form.value.startDate}T${form.value.startTime}`)
  const endDateTime = new Date(`${form.value.endDate}T${form.value.endTime}`)

  if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) return ''

  const duration = endDateTime - startDateTime
  const days = Math.floor(duration / (1000 * 60 * 60 * 24))
  const hours = Math.floor((duration % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

  let summary = `Game runs from ${startDateTime.toLocaleDateString()} at ${form.value.startTime} to ${endDateTime.toLocaleDateString()} at ${form.value.endTime}`

  if (days > 0) {
    summary += ` (${days} day${days > 1 ? 's' : ''}`
    if (hours > 0) summary += ` and ${hours} hour${hours > 1 ? 's' : ''})`
    else summary += ')'
  } else if (hours > 0) {
    summary += ` (${hours} hour${hours > 1 ? 's' : ''})`
  }

  return summary
})

// Validation functions
const validateEndDate = (val) => {
  if (!val || !form.value.startDate) return true

  const startDateTime = new Date(`${form.value.startDate}T${form.value.startTime || '00:00'}`)
  const endDateTime = new Date(`${val}T${form.value.endTime || '23:59'}`)

  return endDateTime > startDateTime || 'End date must be after start date'
}

// Methods
const handleSubmit = () => {
  emit('submit', form.value)
}

// Initialize default dates
const initializeDefaultDates = () => {
  if (!form.value.startDate) {
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    form.value.startDate = today.toISOString().split('T')[0]
    form.value.startTime = '09:00'
    form.value.endDate = tomorrow.toISOString().split('T')[0]
    form.value.endTime = '17:00'
  }
}

// Initialize form with default values
initializeDefaultDates()

// Watch for prop changes
watch(() => props.initialData, (newData) => {
  form.value = { ...form.value, ...newData }
}, { deep: true })
</script>

<style scoped>
/* Modern Cards */
.modern-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.section-header {
  display: flex;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;
}

/* Modern Form Elements */
.modern-input {
  border-radius: 12px;
}

.modern-toggle {
  font-size: 1.1rem;
}

.modern-banner {
  border-radius: 12px;
  border-left: 4px solid #2196f3;
}

/* CTA Buttons */
.cta-primary {
  box-shadow: 0 8px 25px rgba(156, 39, 176, 0.3);
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 1.1rem;
}

.cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(156, 39, 176, 0.4);
}

.cta-secondary {
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 1.1rem;
}

.cta-secondary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(156, 39, 176, 0.2);
}
</style>
