<template>
  <div class="q-pa-none">
    <q-card class="game-map-card">
      <q-card-section class="q-pa-none">
        <!--
          This container will be rendered on both server and client.
          On the server, it will be an empty div with a loading spinner.
          On the client, the `initMap` function will populate it with the Leaflet map.
        -->
        <div ref="mapContainer" class="map-container">
          <div v-if="!isMapInitialized" class="flex flex-center full-height">
            <q-spinner-dots color="primary" size="40px" />
          </div>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, shallowRef, onUnmounted } from 'vue'
import { QCard, QCardSection, QSpinnerDots } from 'quasar'
import { useRoute } from 'vue-router'

// --- COMPOSABLES ---
const route = useRoute()

// --- PROPS ---
const props = defineProps({
  gameListings: {
    type: Array,
    default: () => [],
  },
  gameSlug: {
    type: String,
    default: 'default-game',
  },
})

// --- EMITS ---
const emit = defineEmits(['property-clicked'])

// --- STATE ---
const mapContainer = ref(null)
const leafletMap = ref(null)
const markers = ref({})
const isMapInitialized = ref(false)
const popupTimeouts = ref({})

// Use shallowRef to hold the Leaflet library object. This prevents Vue from
// making the large object deeply reactive, which is a performance best practice.
// It will be populated only on the client.
const L = shallowRef(null)

// --- COMPUTED PROPERTIES ---

// Determine the correct URL format based on current route
const gameUrlFormat = computed(() => {
  // Check if current route is within roundup routes
  if (route.path.includes('/roundup/')) {
    return (listingInGameUuid) => `/roundup/v/${props.gameSlug}/property/${listingInGameUuid}`
  }
  // Default to price-game routes format
  return (listingInGameUuid) => `/game/${props.gameSlug}/property/${listingInGameUuid}`
})

// --- CLIENT-SIDE ONLY LOGIC ---

const createCustomIcon = (color, iconClass = 'home') => {
  // Guard against being called before L is loaded.
  if (!L.value) return null

  return L.value.divIcon({
    html: `<div style="
          background-color: ${color};
          width: 30px;
          height: 30px;
          border-radius: 50%;
          border: 3px solid white;
          box-shadow: 0 2px 6px rgba(0,0,0,0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: bold;
        ">
          <i class="material-icons" style="font-size: 16px;">${iconClass}</i>
        </div>`,
    className: 'custom-div-icon',
    iconSize: [30, 30],
    iconAnchor: [15, 15],
    popupAnchor: [0, -15],
  })
}

// This computed property is now safe because it depends on `L.value`.
// On the server, `L.value` will be null, so it will return an empty array.
// On the client, it will re-evaluate and run correctly after Leaflet is loaded.
const mapProperties = computed(() => {
  if (!L.value) {
    return [] // Return empty array on server or before Leaflet loads
  }

  return props.gameListings
    .map((listing) => {
      // Handle different coordinate property naming patterns
      let latitude, longitude

      // First check for gl_latitude/gl_longitude
      if (listing.gl_latitude && listing.gl_longitude) {
        latitude = listing.gl_latitude
        longitude = listing.gl_longitude
      }
      // Fallback to direct latitude/longitude
      else if (listing.latitude && listing.longitude) {
        latitude = listing.latitude
        longitude = listing.longitude
      }
      // Check nested realty_game_listing object
      else if (
        listing.realty_game_listing?.gl_latitude &&
        listing.realty_game_listing?.gl_longitude
      ) {
        latitude = listing.realty_game_listing.gl_latitude
        longitude = listing.realty_game_listing.gl_longitude
      }
      // Check nested sale_listing object
      else if (
        listing.sale_listing?.latitude &&
        listing.sale_listing?.longitude
      ) {
        latitude = listing.sale_listing.latitude
        longitude = listing.sale_listing.longitude
      }

      if (!latitude || !longitude) {
        return null
      }

      // debugger /roundup/v/${props.gameSlug}/property/${listingInGameUuid}
      let listingInGameUuid =
        listing.realty_game_listing?.uuid ||
        listing.uuid
      return {
        id: listing.rgl_uuid || listing.uuid || listing.id,
        title: listing.gl_title_atr || listing.title || 'Property',
        address: listing.gl_vicinity_atr || listing.address || '',
        price:
          listing.sale_listing?.formatted_display_price ||
          listing.price ||
          'N/A',
        gameUrl: gameUrlFormat.value(listingInGameUuid),
        bgImage:
          listing.gl_image_url_atr ||
          listing.sale_listing?.sale_listing_pics?.[0]?.image_details?.url ||
          'https://dummyimage.com/200x100?text=.',
        lat: parseFloat(latitude),
        lng: parseFloat(longitude),
        listing: listing,
        icon: createCustomIcon('#4CAF50', 'home'),
      }
    })
    .filter(Boolean)
})

const initMap = () => {
  if (!mapContainer.value || !L.value || mapProperties.value.length === 0)
    return

  leafletMap.value = L.value
    .map(mapContainer.value)
    .setView([51.505, -0.09], 15)

  L.value
    .tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution:
        '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
    })
    .addTo(leafletMap.value)

  const bounds = L.value.latLngBounds()
  mapProperties.value.forEach((property) => {
    if (property.lat && property.lng) {
      const marker = L.value
        .marker([property.lat, property.lng], { icon: property.icon })
        .addTo(leafletMap.value)

      // Create enhanced popup content with image and link
      // const gameSlug =
      //   property.listing.realty_game_listing?.uuid ||
      //   property.listing.rgl_uuid ||
      //   property.id
      // const listingInGameUuid =
      //   property.listing.realty_game_listing?.uuid ||
      //   property.listing.rgl_uuid ||
      //   property.id

      //       <div class="popup-address">${property.address}</div>
      // <div class="popup-price">${property.price}</div>

      const popupContent = `
        <div class="map-popup-content">
          <div class="popup-overlay">
            <div class="popup-image-container">
              <img src="${property.bgImage}" alt="${property.title}" class="popup-image" />
            </div>
            <div class="popup-info">
              <div class="popup-title">${property.title}</div>
              <div class="popup-address">${property.address}</div>
              <div class="popup-actions">
                <a href="${property.gameUrl}" 
                   class="popup-guess-btn"
                   onclick="event.stopPropagation()">
                  🎯 Guess Price
                </a>
              </div>
            </div>
          </div>
        </div>
      `

      // Bind popup and set it to open on hover
      marker.bindPopup(popupContent, {
        closeOnClick: false,
        autoClose: false,
        closeButton: true,
        className: 'custom-popup',
        offset: [0, -10], // Slightly offset to make it easier to hover
      })

      // Improved hover behavior with timeouts
      marker.on('mouseover', () => {
        // Clear any existing timeout for this marker
        if (popupTimeouts.value[property.id]) {
          clearTimeout(popupTimeouts.value[property.id])
          delete popupTimeouts.value[property.id]
        }
        marker.openPopup()
      })

      marker.on('mouseout', () => {
        // Add a delay before closing to allow users to move to the popup
        popupTimeouts.value[property.id] = setTimeout(() => {
          marker.closePopup()
          delete popupTimeouts.value[property.id]
        }, 300) // 300ms delay
      })

      // Add event listeners to keep popup open when hovering over it
      marker.on('popupopen', () => {
        const popup = marker.getPopup()
        const popupElement = popup.getElement()

        if (popupElement) {
          // Keep popup open when hovering over popup content
          popupElement.addEventListener('mouseenter', () => {
            if (popupTimeouts.value[property.id]) {
              clearTimeout(popupTimeouts.value[property.id])
              delete popupTimeouts.value[property.id]
            }
          })

          // Close popup when leaving popup content (with delay)
          popupElement.addEventListener('mouseleave', () => {
            popupTimeouts.value[property.id] = setTimeout(() => {
              marker.closePopup()
              delete popupTimeouts.value[property.id]
            }, 200) // Shorter delay when leaving popup
          })
        }
      })

      // Keep existing click behavior
      marker.on('click', () => {
        panAndZoomToLocation(property)
        emit('property-clicked', property.listing)
      })

      bounds.extend([property.lat, property.lng])
      markers.value[property.id] = marker
    }
  })

  if (bounds.isValid()) {
    leafletMap.value.fitBounds(bounds, { padding: [50, 50] })
  }

  isMapInitialized.value = true
}

const panAndZoomToLocation = (property) => {
  if (leafletMap.value && property.lat && property.lng) {
    leafletMap.value.setView([property.lat, property.lng], 17)
    markers.value[property.id]?.openPopup()
  }
}

// onMounted is the key. It ONLY runs on the client.
onMounted(async () => {
  // Double-check we're in a browser environment
  if (typeof window !== 'undefined') {
    try {
      // Dynamically import Leaflet library
      const leaflet = await import('leaflet')
      L.value = leaflet

      // Dynamically import Leaflet CSS
      await import('leaflet/dist/leaflet.css')

      // Fix for default markers now that L is loaded
      delete L.value.Icon.Default.prototype._getIconUrl
      L.value.Icon.Default.mergeOptions({
        iconRetinaUrl:
          'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
        iconUrl:
          'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
        shadowUrl:
          'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
      })

      // Now that everything is loaded, initialize the map
      initMap()
    } catch (e) {
      console.error('Error loading Leaflet:', e)
    }
  }
})

// Cleanup timeouts on unmount
onUnmounted(() => {
  Object.values(popupTimeouts.value).forEach((timeout) => {
    clearTimeout(timeout)
  })
  popupTimeouts.value = {}
})
</script>

<style scoped>
.game-map-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
}

.map-container {
  height: 400px;
  width: 100%;
  background-color: #e0e0e0;
  border-radius: 8px;
  z-index: 1;
  position: relative;
}

/* Deep selectors are needed to style Leaflet's dynamically generated HTML */
:deep(.custom-div-icon) {
  background: transparent;
  border: none;
}

:deep(.leaflet-popup-content-wrapper) {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.leaflet-popup-content) {
  margin: 0;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.leaflet-popup-tip) {
  background: white;
}

/* Leaflet control styles */
:deep(.leaflet-control-zoom) {
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.leaflet-control-zoom a) {
  border-radius: 6px;
  color: #333;
  font-weight: bold;
}

:deep(.leaflet-control-attribution) {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  font-size: 11px;
}

/* Custom styles for the popup's inner content */
:deep(.map-popup-content) {
  width: 280px;
  min-height: 180px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  font-family: 'Roboto', sans-serif;
}

:deep(.popup-overlay) {
  background: white;
  display: flex;
  flex-direction: column;
  height: 100%;
}

:deep(.popup-image-container) {
  width: 100%;
  height: 120px;
  overflow: hidden;
  position: relative;
}

:deep(.popup-image) {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

:deep(.popup-info) {
  padding: 12px;
  background: white;
  color: #333;
}

:deep(.popup-title) {
  margin: 0 0 6px 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.3;
  color: #1a1a1a;
}

:deep(.popup-address) {
  margin: 0 0 4px 0;
  font-size: 13px;
  color: #666;
  line-height: 1.2;
}

:deep(.popup-price) {
  margin: 0 0 12px 0;
  font-size: 15px;
  font-weight: 600;
  color: #2e7d32;
  line-height: 1.2;
}

:deep(.popup-actions) {
  margin-top: 8px;
  padding-top: 4px; /* Add a bit more padding to make button easier to click */
}

:deep(.popup-guess-btn) {
  display: inline-block;
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  text-decoration: none;
  padding: 10px 18px; /* Slightly larger padding for easier clicking */
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(25, 118, 210, 0.3);
  cursor: pointer;
  min-width: 120px; /* Ensure button has minimum width */
  text-align: center;
}

:deep(.popup-guess-btn:hover) {
  background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(25, 118, 210, 0.4);
  color: white;
  text-decoration: none;
}

:deep(.custom-popup .leaflet-popup-content-wrapper) {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  pointer-events: auto; /* Ensure popup can receive mouse events */
}

:deep(.custom-popup .leaflet-popup-content) {
  margin: 0;
  border-radius: 8px;
  overflow: hidden;
  width: 280px !important;
  pointer-events: auto; /* Ensure popup content can receive mouse events */
}

:deep(.custom-popup .leaflet-popup-tip) {
  background: white;
  pointer-events: none; /* Tip doesn't need mouse events */
}

:deep(.custom-popup) {
  pointer-events: auto; /* Ensure entire popup can receive mouse events */
}

:deep(.popup-text-scrim) {
  padding: 10px;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.85) 0%,
    rgba(0, 0, 0, 0) 100%
  );
}

:deep(.popup-title) {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: bold;
  line-height: 1.2;
}

:deep(.popup-address),
:deep(.popup-price) {
  margin: 2px 0 0 0;
  font-size: 12px;
  line-height: 1.2;
}
</style>
