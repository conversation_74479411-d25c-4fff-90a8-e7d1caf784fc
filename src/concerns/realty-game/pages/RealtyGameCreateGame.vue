<template>
  <q-page class="modern-create-game-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-gradient"></div>
        <div class="hero-pattern"></div>
      </div>
      <div class="max-ctr">
        <div class="hero-content q-px-xl q-py-md">
          <div class="row items-center min-height-hero">
            <div class="col-12">
              <div class="hero-text text-center">
                <div class="hero-badge q-mb-md">
                  <q-chip
                    color="accent"
                    text-color="white"
                    icon="sports_esports"
                    class="q-px-md"
                  >
                    🎮 Game Creator
                  </q-chip>
                </div>
                <h1 class="hero-title animate-slide-up text-white">
                  Create Your Own
                  <span class="text-primary">Property Price </span>
                  Game
                </h1>
                <p class="hero-subtitle animate-fade-in-delayed text-h6 q-mt-md q-mb-xl">
                  Set up a new price guessing game with custom settings and challenge your friends!
                </p>
                <!-- <div class="q-mb-md">
                  <q-btn flat
                         color="white"
                         icon="arrow_back"
                         label="Back to Admin"
                         class="cta-secondary"
                         @click="$router.push({ name: 'rSubdomainRoot' })" />
                </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Form Section -->
    <section class="form-section q-py-xl">
      <div class="max-ctr q-px-sm">

        <div class="max-width-form">
          <!-- Step 1: Email and Property URL -->
          <GameCreationInitialStep
            v-if="currentStep === 1"
            :loading="false"
            :initial-data="initialStepData"
            @submit="handleInitialStepSubmit"
            @cancel="$router.push({ name: 'rSubdomainRoot' })" />

          <!-- Step 2: Game Creation Form -->
          <div v-else-if="currentStep === 2">
            <!-- Step Progress -->
            <div class="step-progress q-mb-lg">
              <q-stepper
                v-model="currentStep"
                color="primary"
                animated
                flat
                class="modern-stepper">
                <q-step
                  :name="1"
                  title="Contact & Property"
                  icon="person"
                  :done="currentStep > 1" />
                <q-step
                  :name="2"
                  title="Game Setup"
                  icon="sports_esports"
                  :active="currentStep === 2" />
              </q-stepper>
            </div>

            <!-- Back Button -->
            <div class="q-mb-md">
              <q-btn flat
                     color="grey-7"
                     icon="arrow_back"
                     label="Back to Contact Info"
                     @click="currentStep = 1" />
            </div>

            <GameCreationForm
              :loading="isCreating"
              @submit="createGame"
              @cancel="$router.push({ name: 'rSubdomainRoot' })" />
          </div>
        </div>
      </div>
    </section>
  </q-page>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { useRealtyGame } from '../composables/useRealtyGame'
import GameCreationForm from '../components/GameCreationForm.vue'
import GameCreationInitialStep from '../components/GameCreationInitialStep.vue'

const $router = useRouter()
const $q = useQuasar()

// Initialize the realty game composable
const { createRealtyGameByUser } = useRealtyGame()

// Multi-step form state
const currentStep = ref(1)
const initialStepData = ref({
  email: '',
  propertyUrl: ''
})

// Loading state
const isCreating = ref(false)

// Methods
const handleInitialStepSubmit = (data) => {
  initialStepData.value = data
  currentStep.value = 2

  $q.notify({
    color: 'positive',
    message: 'Contact information saved! Now set up your game.',
    icon: 'check'
  })
}

const createGame = async (formData) => {
  isCreating.value = true

  try {
    // Combine date and time for API
    const startDateTime = new Date(`${formData.startDate}T${formData.startTime}`)
    const endDateTime = new Date(`${formData.endDate}T${formData.endTime}`)

    const gameData = {
      title: formData.title,
      description: formData.description,
      start_time: startDateTime.toISOString(),
      end_time: endDateTime.toISOString(),
      is_active: formData.isActive,
      allow_anonymous: formData.allowAnonymous,
      max_players: formData.maxPlayers || null,
      // Include the initial step data
      creator_email: initialStepData.value.email,
      initial_property_url: initialStepData.value.propertyUrl
    }

    // Create the game using the API
    await createRealtyGameByUser(gameData)

    $q.notify({
      color: 'positive',
      message: 'Game created successfully!',
      icon: 'check'
    })

    // Navigate back to admin page
    $router.push({ name: 'rSubdomainRoot' })

  } catch (error) {
    console.error('Error creating game:', error)
    $q.notify({
      color: 'negative',
      message: 'Failed to create game. Please try again.',
      icon: 'error'
    })
  } finally {
    isCreating.value = false
  }
}
</script>

<style scoped>
/* Modern Create Game Page Styles */
.modern-create-game-page {
  overflow-x: hidden;
}

/* Hero Section */
.hero-section {
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #1f3393 0%, #9c27b0 50%, #26a69a 100%);
  opacity: 0.95;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(255, 255, 255, 0.1) 2px,
      transparent 2px
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(255, 255, 255, 0.1) 2px,
      transparent 2px
    );
  background-size: 60px 60px;
}

.hero-title {
  font-size: 3rem;
  font-weight: 800;
  line-height: 1.1;
  color: white;
  margin-bottom: 1.5rem;
}

.hero-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  line-height: 1.6;
}

.min-height-hero {
  min-height: 60vh;
}

/* Animations */
.animate-slide-up {
  animation: slideUp 0.8s ease-out forwards;
  opacity: 0;
}

.animate-fade-in-delayed {
  animation: fadeInDelayed 1s ease-out 0.3s forwards;
  opacity: 0;
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDelayed {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* CTA Buttons */
.cta-primary {
  box-shadow: 0 8px 25px rgba(156, 39, 176, 0.3);
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 1.1rem;
}

.cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(156, 39, 176, 0.4);
}

.cta-secondary {
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 1.1rem;
}

.cta-secondary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(156, 39, 176, 0.2);
}

/* Form Section */
.form-section {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
  position: relative;
  overflow: hidden;
}

.form-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23e0e7ff" opacity="0.3"/><circle cx="75" cy="75" r="1" fill="%23e0e7ff" opacity="0.3"/><circle cx="50" cy="10" r="0.5" fill="%23e0e7ff" opacity="0.4"/><circle cx="10" cy="60" r="0.5" fill="%23e0e7ff" opacity="0.4"/><circle cx="90" cy="40" r="0.5" fill="%23e0e7ff" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.max-width-form {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

/* Modern Cards */
.modern-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.section-header {
  display: flex;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;
}

/* Modern Form Elements */
.modern-input {
  border-radius: 12px;
}

.modern-toggle {
  font-size: 1.1rem;
}

.modern-banner {
  border-radius: 12px;
  border-left: 4px solid #2196f3;
}

/* Step Progress */
.step-progress {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.modern-stepper {
  background: transparent;
  box-shadow: none;
}

/* Responsive */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .min-height-hero {
    min-height: 50vh;
  }
}
</style>
