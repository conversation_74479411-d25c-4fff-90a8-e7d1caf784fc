# RealtyGamePagesLayout.vue

A comprehensive layout component that provides a consistent structure and data management for all realty game pages. This component serves as the main wrapper for realty game routes and implements efficient data fetching strategies with multiple fallback mechanisms.

## Overview

The `RealtyGamePagesLayout` component is designed to:
- Provide a consistent header display for property-specific routes
- Implement efficient data fetching with intelligent caching
- Handle session management and currency conversion
- Pass comprehensive props to child route components
- Manage navigation and user interactions

## Key Features

### 🚀 Efficient Data Fetching Strategy

The component implements a three-tier data fetching strategy for optimal performance:

1. **Primary**: Check if property data exists in already-loaded game listings (most efficient)
2. **Secondary**: Fetch individual property data if not available in game listings
3. **Tertiary**: Use results data as fallback for results pages

This approach minimizes API calls and provides fast loading times by reusing existing data whenever possible.

### 🎯 Smart Property Header

- Displays property title, address, and thumbnail
- Responsive design with mobile optimizations
- Interactive header that navigates to property page when clicked
- Automatic fallback to city/postal code when street address unavailable
- Image filtering to hide flagged images

### 💱 Currency Management

- Initializes currency from session storage
- Falls back to game default currency
- Integrates with currency converter for price display
- Maintains currency selection across route changes

### 🔄 Session Handling

- Manages current user session identification
- Handles shareable session URLs
- Determines session ownership for personalized content
- Supports session-based data persistence

## Props Passed to Child Components

The layout passes comprehensive props to child route components via `<router-view>`:

### Session & Routing Data
- `currPlayerSssnId`: Current player's session ID
- `gameSessionId`: Game session identifier
- `routeSssnId`: Route-specific session ID
- `shareableResultsUrl`: URL for sharing results

### Property Data
- `propertyUuid`: Current property identifier
- `currentPropertyListing`: Complete property data object
- `isPropertyLoading`: Loading state for property data
- `propertyError`: Error state for property fetching

### Game Data
- `gameTitle`: Title of the current game
- `gameDefaultCurrency`: Default currency for the game
- `totalProperties`: Total number of properties in game
- `gameCommunitiesDetails`: Community-specific game data
- `firstPropListing`: First property in the game
- `realtyGameSummary`: Summary of game statistics

### Results Data
- `results`: Game results data
- `playerResults`: Individual player results
- `comparisonSummary`: Comparison with other players
- `gameBreakdown`: Detailed game breakdown
- `overallRanking`: Player's overall ranking
- `leaderboard`: Game leaderboard data
- `showLdrboard`: Whether to display leaderboard

### Utility Functions
- `formatPriceWithBothCurrencies`: Currency formatting function
- `getScoreColor`: Score-based color calculation

### State Management
- `isLoading`: Global loading state
- `error`: Global error state
- `ssGameSession`: Server-side game session data

## Events Handled

### @load-results
Triggered when child components need to load results data.
```javascript
// Usage in child component
emit('load-results')
```

### @update-progress
Handles progress updates from child components.
```javascript
// Usage in child component
emit('update-progress', { current: 3, total: 5 })
```

### @game-complete
Triggered when the game is completed, navigates to results page.
```javascript
// Usage in child component
emit('game-complete', sessionId)
```

## Data Structure

### Property Data Object
```javascript
{
  realty_game_listing: {
    gl_title_atr: "Property Title",
    // ... other game-specific listing data
  },
  sale_listing: {
    street_address: "123 Main Street",
    city: "City Name",
    postal_code: "12345",
    region: "Region Name",
    sale_listing_pics: [
      {
        flag_is_hidden: false,
        image_details: {
          url: "https://example.com/image.jpg"
        }
      }
    ]
    // ... other sale listing data
  }
}
```

## Performance Optimizations

### 1. Computed Properties
All reactive data uses computed properties for efficient reactivity:
- `currentPropertyListing`: Combines data from multiple sources
- `propertyImages`: Filters and processes property images
- `showPropertyHeader`: Determines header visibility
- `shareableResultsUrl`: Generates shareable URLs

### 2. Intelligent Caching
- Checks existing game listings before making API calls
- Reuses fetched data across route changes
- Implements fallback data sources

### 3. Lazy Loading
- Property data only fetched when needed
- API calls triggered by route changes
- Efficient watchers for minimal re-renders

### 4. Error Handling
- Graceful degradation when data unavailable
- Console logging for debugging
- Fallback UI states for missing data

## Responsive Design

### Mobile Optimizations
- Property thumbnail hidden on mobile devices (`mobile-hide` class)
- Responsive header layout with proper text wrapping
- Touch-friendly interactive elements

### CSS Classes
- `.clickable`: Applied to header when navigation is available
- `.mobile-hide`: Hides elements on mobile devices
- `.layout-rgpage-prop-header`: Main header styling
- `.thumbnail-container`: Property image container
- `.thumbnail-placeholder`: Fallback when no images

## Usage Examples

### Basic Route Setup
```javascript
// In router configuration
{
  path: '/game/:gameSlug',
  component: RealtyGamePagesLayout,
  children: [
    {
      path: 'property/:propertyUuid',
      name: 'rPriceGameProperty',
      component: PropertyPage
    },
    {
      path: 'results/:routeSssnId',
      name: 'rPriceGameResults',
      component: ResultsPage
    }
  ]
}
```

### Accessing Props in Child Components
```javascript
// In child component
export default {
  props: [
    'currentPropertyListing',
    'gameTitle',
    'formatPriceWithBothCurrencies',
    // ... other props
  ],
  
  computed: {
    propertyTitle() {
      return this.currentPropertyListing?.realty_game_listing?.gl_title_atr
    }
  }
}
```

## Testing

The component includes comprehensive tests covering:
- Component mounting and basic functionality
- Property header display logic
- Data fetching strategies and fallbacks
- Session and currency management
- Navigation and event handling
- Responsive design features
- Error handling and edge cases

Run tests with:
```bash
npm test src/concerns/realty-game/tests/RealtyGamePagesLayout.test.js
```

## Dependencies

### Vue Composition API
- `ref`, `computed`, `onMounted`, `watch`
- `useRoute`, `useRouter` from Vue Router

### Custom Composables
- `useSingleListingRealtyGame`: Game data management
- `useRealtyGameStorage`: Session and storage handling
- `useServerSingleListingGameResults`: Results data management
- `useCurrencyConverter`: Currency conversion utilities

### Quasar Components
- `QIcon`: Icon display component

## Browser Support

- Modern browsers with ES6+ support
- Mobile browsers (iOS Safari, Chrome Mobile)
- Responsive design for all screen sizes

## Contributing

When modifying this component:
1. Maintain the three-tier data fetching strategy
2. Ensure all props are properly passed to child components
3. Add appropriate test coverage for new features
4. Update documentation for any API changes
5. Test responsive behavior on mobile devices
