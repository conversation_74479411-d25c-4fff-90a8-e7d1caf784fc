<!DOCTYPE html>
<html lang="en-US" dir="ltr">
<head>
  <title>Engaging Property Price Challenge - Star Home Team</title>
  <meta name="description" content="Dive into our exciting house price guessing game! Test your real estate knowledge, learn market trends, and discover new properties every week with Star Home Team.">
  <meta name="keywords" content="property price game, real estate challenge, property valuation, house price quiz, market knowledge test">
  <meta property="og:title" content="Engaging Property Price Challenge">
  <meta property="og:description" content="Dive into our exciting house price guessing game! Test your real estate knowledge, learn market trends, and discover new properties every week.">
  <meta property="og:image" content="https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg">
  <meta property="og:url" content="https://housepriceguess.com">
  <meta property="og:type" content="website">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="Engaging Property Price Challenge">
  <meta name="twitter:description" content="Dive into our exciting house price guessing game! Test your real estate knowledge, learn market trends, and discover new properties every week.">
  <meta name="twitter:image" content="https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg">
  <meta name="twitter:url" content="https://housepriceguess.com">
  <meta charset="utf-8">
  <meta name="viewport" content="user-scalable=no,initial-scale=1,maximum-scale=1,minimum-scale=1,width=device-width">
  <link rel="icon" type="image/png" sizes="128x128" href="/icons/favicon-128x128.png">
  <!-- Quasar CDN Links -->
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900|Material+Icons" rel="stylesheet" type="text/css">
  <link href="https://cdn.jsdelivr.net/npm/quasar@2/dist/quasar.prod.css" rel="stylesheet" type="text/css">
  <style>
    /* Enhanced styles for engagement: Vibrant accents, animations */
    body { background-color: #121212; color: #ffffff; font-family: Roboto, sans-serif; }
    .q-card { background-color: #1e1e1e; border: 1px solid #333; border-radius: 8px; }
    .q-header { background: linear-gradient(to right, #1f3393, #339933); }
    .q-footer { background-color: #339933; }
    .text-primary { color: #1f3393 !important; }
    .branded-game-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
    .game-listing-card { transition: transform 0.3s, box-shadow 0.3s; }
    .game-listing-card:hover { transform: translateY(-5px); box-shadow: 0 4px 20px rgba(31, 51, 147, 0.5); }
    .q-banner { background-color: #26a69a; color: #fff; border-radius: 8px; }
    .q-timeline__entry { animation: fadeIn 0.5s; }
    @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
  </style>
  <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "Engaging House Price Guess",
      "description": "Fun property price guessing games to learn about real estate markets, with weekly updates for endless challenges.",
      "url": "https://www.housepriceguess.com"
    }
  </script>
</head>
<body>
  <div id="q-app">
    <q-layout view="hHh LpR fFf">
      <q-header elevated class="text-white">
        <q-toolbar>
          <q-toolbar-title>
            <a href="https://star.housepriceguess.com/" class="logo-link">
              <img src="https://images.squarespace-cdn.com/content/v1/611ecc9714315f19333cb5e0/9895a9fe-0b9f-4bc8-b815-ff49fbb72df8/TheStarHomeTeam_75907490_BHGRE_StarHomes_Horizontal_WhiteonGreen_RGB01+%281%29.jpg?format=1500w" alt="Star Home Team Logo" style="height:45px;">
            </a>
            Star Home Team House Prices Game
          </q-toolbar-title>
        </q-toolbar>
      </q-header>

      <q-page-container>
        <q-page padding>
          <div class="text-h3 text-weight-bold q-mb-md text-center text-primary">Dive into the Exciting World of Property Prices!</div>
          <p class="text-h6 text-grey-5 text-center q-mb-xl">Since 1998, The Star Home Team has been your trusted partner in real estate across Northern Illinois & Southern Wisconsin. Now, sharpen your skills with our thrilling interactive game!</p>

          <!-- How to Play Section -->
          <q-card class="q-mb-xl" flat bordered>
            <q-card-section>
              <div class="text-h5 text-weight-bold q-mb-md text-center">How to Play the House Price Game</div>
              <q-timeline color="primary" layout="comfortable">
                <q-timeline-entry heading>Step 1: Select a Property</q-timeline-entry>
                <q-timeline-entry title="Browse Listings" side="left" icon="search">
                  <p>Choose from our curated selection of real properties in Grayslake, IL. Each card shows key details like bedrooms, bathrooms, and garage spaces.</p>
                </q-timeline-entry>
                <q-timeline-entry title="View Details" side="right" icon="visibility">
                  <p>Click 'Play' to see high-quality photos, floor plans, and neighborhood info without the price revealed.</p>
                </q-timeline-entry>
                <q-timeline-entry title="Make Your Guess" side="left" icon="monetization_on">
                  <p>Based on the info, guess the recent sale price. Be as accurate as possible!</p>
                </q-timeline-entry>
                <q-timeline-entry title="Get Feedback" side="right" icon="feedback">
                  <p>See how close your guess was, learn the actual price, and get tips on what factors influenced it.</p>
                </q-timeline-entry>
                <q-timeline-entry title="Repeat & Improve" side="left" icon="repeat">
                  <p>Play multiple properties to track your progress and become a market expert!</p>
                </q-timeline-entry>
              </q-timeline>
            </q-card-section>
          </q-card>

          <!-- Benefits Section -->
          <q-card class="q-mb-xl" flat bordered>
            <q-card-section>
              <div class="text-h5 text-weight-bold q-mb-md text-center">Why Play? Unlock Real Estate Insights!</div>
              <q-list>
                <q-item>
                  <q-item-section avatar><q-icon name="school" color="positive" /></q-item-section>
                  <q-item-section>Learn pricing factors like location, size, and amenities to make informed buying/selling decisions.</q-item-section>
                </q-item>
                <q-item>
                  <q-item-section avatar><q-icon name="trending_up" color="positive" /></q-item-section>
                  <q-item-section>Understand market trends in Northern Illinois & Southern Wisconsin through real examples.</q-item-section>
                </q-item>
                <q-item>
                  <q-item-section avatar><q-icon name="people" color="positive" /></q-item-section>
                  <q-item-section>Compete with friends, share scores, and discuss what makes properties valuable.</q-item-section>
                </q-item>
                <q-item>
                  <q-item-section avatar><q-icon name="lightbulb" color="positive" /></q-item-section>
                  <q-item-section>Gain confidence in real estate knowledge for investments or personal moves.</q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>

          <!-- Weekly Updates Banner -->
          <q-banner class="q-mb-xl text-center" rounded>
            <template v-slot:avatar>
              <q-icon name="update" color="white" size="lg" />
            </template>
            <div class="text-h6">Weekly Fresh Challenges!</div>
            <p>Every week, we select new exciting properties for the game. Revisit often to discover the latest listings and keep your skills sharp. Next update: Coming Soon!</p>
            <q-btn color="white" text-color="accent" label="Bookmark Now" flat />
          </q-banner>

          <!-- Property Grid -->
          <div class="text-h5 text-weight-bold q-mb-md text-center">This Week's Properties</div>
          <div class="branded-game-grid q-mt-lg">
            <q-card class="game-listing-card" flat bordered v-for="property in properties" :key="property.id">
              <q-img :src="property.image" :ratio="4/3" basic>
                <div class="absolute-bottom text-subtitle2">{{ property.address }}</div>
              </q-img>
              <q-card-section>
                <div class="text-caption text-grey"><q-icon name="place" /> {{ property.location }}</div>
              </q-card-section>
              <q-card-section class="q-pt-none">
                <div class="row items-center q-gutter-md">
                  <q-tooltip>Bedrooms</q-tooltip><q-icon name="king_bed" /> {{ property.beds }}
                  <q-tooltip>Bathrooms</q-tooltip><q-icon name="bathtub" /> {{ property.baths }}
                  <q-tooltip>Garage</q-tooltip><q-icon name="garage" /> {{ property.garage }}
                </div>
              </q-card-section>
              <q-separator />
              <q-card-actions align="around">
                <q-btn flat color="primary" label="Play Now!" @click="playGame(property.id)" />
                <q-btn flat round color="primary" icon="share" @click="shareProperty(property.id)" />
              </q-card-actions>
            </q-card>
          </div>

          <!-- Social Sharing -->
          <q-card class="q-mt-xl q-mb-xl" flat bordered>
            <q-card-section class="text-center">
              <div class="text-h6 text-weight-bold q-mb-md">Spread the Fun!</div>
              <p class="text-body1">Challenge your friends and see who knows the market best!</p>
              <div class="row justify-center q-mt-md">
                <q-btn icon="mdi-facebook" color="blue" round class="q-ma-sm" href="//www.facebook.com/sharer/sharer.php?u=https://star.housepriceguess.com/" target="_blank" />
                <q-btn icon="mdi-twitter" color="light-blue" round class="q-ma-sm" href="https://x.com/intent/tweet?text=Check out this fun house price game: https://star.housepriceguess.com/" target="_blank" />
                <q-btn icon="mdi-whatsapp" color="green" round class="q-ma-sm" href="https://wa.me/?text=Check out this fun house price game: https://star.housepriceguess.com/" target="_blank" />
                <q-btn icon="mdi-linkedin" color="blue-grey" round class="q-ma-sm" href="//www.linkedin.com/shareArticle?mini=true&url=https://star.housepriceguess.com/&title=Fun House Price Guessing Game" target="_blank" />
                <q-btn icon="mdi-email" color="orange" round class="q-ma-sm" href="mailto:?subject=Join the House Price Challenge!&body=Check out this engaging game: https://star.housepriceguess.com/" target="_blank" />
              </div>
            </q-card-section>
          </q-card>
        </q-page>
      </q-page-container>

      <q-footer elevated class="text-white">
        <q-toolbar>
          <q-toolbar-title class="text-center">
            <a href="/i/privacy" class="text-white q-mr-md">Privacy Policy</a> |
            <a href="/i/terms-of-service" class="text-white q-ml-md">Terms Of Service</a>
            <div class="float-right">Powered by <a href="https://housepriceguess.com/" class="text-white">HousePriceGuess</a></div>
          </q-toolbar-title>
        </q-toolbar>
      </q-footer>
    </q-layout>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.prod.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/quasar@2/dist/quasar.umd.prod.js"></script>
  <script>
    const { createApp } = Vue;
    const app = createApp({
      setup() {
        return {
          properties: [
            { id: 1, address: '873 Essex Circle 873 Grayslake IL 60030', location: 'Grayslake, IL, USA', image: 'https://d25fhp1qfwqa2h.cloudfront.net/a2e81d3b02df7e9fb32d38593738d096,1757012718000_245_170?phid=16247539538', beds: 2, baths: 1.5, garage: 2 },
            { id: 2, address: '1544 Syracuse Drive Grayslake IL 60030', location: 'Grayslake, IL, USA', image: 'https://d25fhp1qfwqa2h.cloudfront.net/0b5db96906189de3d30731ee158ef340,1754589921000_auto_650?phid=15730487056', beds: 3, baths: 3.5, garage: 2 },
            { id: 3, address: '872 Tylerton Circle Grayslake IL 60030', location: 'Grayslake, IL, USA', image: 'https://d25fhp1qfwqa2h.cloudfront.net/d0fea358ed43cd3b2923131aac0fbe5b,1756133421000_auto_650?phid=16052196327', beds: 3, baths: 3.5, garage: 2 },
            { id: 4, address: '18564 W Main Street Grayslake IL 60030', location: 'Grayslake, IL, USA', image: 'https://d25fhp1qfwqa2h.cloudfront.net/086afaeff6f5f45008c9a71c5c82093c,1754690120000_auto_650?phid=15754747683', beds: 3, baths: 2, garage: 1 },
            { id: 5, address: '2814 Phillip Drive Grayslake IL 60030', location: 'Grayslake, IL, USA', image: 'https://d25fhp1qfwqa2h.cloudfront.net/668f30d16ec1c9f6eb21f77536c9a8bd,1714507382000_auto_650?phid=9393976934', beds: 3, baths: 2.5, garage: 3 }
          ]
        };
      },
      methods: {
        playGame(id) { alert(`Starting game for property ID: ${id}. View details and guess the price!`); },
        shareProperty(id) { alert(`Sharing property ID: ${id}. Invite friends to guess too!`); }
      }
    });
    app.use(Quasar, { config: {} });
    app.mount('#q-app');
  </script>
</body>
</html>
