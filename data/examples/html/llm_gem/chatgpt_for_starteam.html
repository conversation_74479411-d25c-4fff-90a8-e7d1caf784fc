<!DOCTYPE html>
<html lang="en-GB" dir="ltr">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Property Price Challenge — Reimagined</title>
  <meta name="description" content="Play the weekly House Price Game: guess prices, learn market signals, and come back each week for new properties." />
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900|Material+Icons" rel="stylesheet" />
  <!-- Quasar CSS (CDN) -->
  <link href="https://cdn.jsdelivr.net/npm/quasar@2/dist/quasar.prod.css" rel="stylesheet" />

  <style>
    :root{
      --brand:#1f3393;
      --accent:#26a69a;
      --bg:#0f1113;
      --card:#121316;
      --muted: #9aa3b2;
    }

    html,body,#q-app { height:100%; margin:0; font-family: <PERSON><PERSON>, <PERSON><PERSON>, sans-serif; background: linear-gradient(180deg,#071028 0%, #0b1220 60%); color:#fff; }

    /* Header */
    .site-logo { height:44px; border-radius:6px; }

    /* Hero */
    .hero {
      background: linear-gradient(90deg, rgba(31,51,147,0.12), rgba(38,166,154,0.06));
      border-radius: 12px;
      padding: 28px;
      margin-bottom: 20px;
      box-shadow: 0 6px 30px rgba(5,8,18,.5);
      backdrop-filter: blur(6px);
    }
    .pulse-cta {
      display:inline-block;
      animation: pulse 2.4s infinite;
      transform-origin:center;
    }
    @keyframes pulse {
      0%{ transform:scale(1); }
      50%{ transform:scale(1.03); }
      100%{ transform:scale(1); }
    }

    /* Grid */
    .branded-game-grid { display:grid; grid-template-columns: repeat(auto-fit, minmax(260px,1fr)); gap:18px; margin-top:18px; }
    .game-listing-card { background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0.01)); border: 1px solid rgba(255,255,255,0.04); transition: transform .22s ease, box-shadow .22s ease; }
    .game-listing-card:hover { transform: translateY(-6px); box-shadow: 0 12px 30px rgba(0,0,0,0.6); }

    /* How to play */
    .steps .q-card { background: transparent; border: 1px dashed rgba(255,255,255,0.06); }

    /* Countdown */
    .countdown { font-weight:600; color:var(--accent); }

    /* small helpers */
    .muted { color: var(--muted); }
    .badge { background: linear-gradient(90deg,var(--brand), #5b6be6); color:#fff; padding:6px 10px; border-radius:999px; font-weight:600; font-size:0.85rem; }
    .learn-pill { background: rgba(255,255,255,0.03); padding:6px 10px; border-radius:999px; font-size:0.85rem; }
    a.no-underline { text-decoration: none; color: inherit; }
    .stat { font-size:1.2rem; font-weight:700; }
    @media (max-width:600px){ .hero { padding:16px; } }
  </style>

  <script type="application/ld+json">
  {
    "@context":"https://schema.org",
    "@type":"WebSite",
    "name":"House Price Guess - Property Price Challenge",
    "url":"https://housepriceguess.com",
    "description":"Weekly property price guessing game that helps you learn local market signals."
  }
  </script>
</head>

<body>
  <div id="q-app">
    <q-layout view="lHh Lpr lFf" class="bg-transparent">
      <!-- HEADER -->
      <q-header elevated class="bg-transparent q-pa-sm" height-hint="64">
        <q-toolbar class="container row items-center">
          <div class="row items-center">
            <a href="https://star.housepriceguess.com/" class="no-underline" aria-label="House Price Guess home">
              <img class="site-logo q-mr-md" src="https://www.housepriceguess.com/icons/favicon-128x128.png" alt="HPG logo">
            </a>
            <div>
              <div class="text-h6" style="color:var(--brand)">House Price Guess</div>
              <div class="muted" style="font-size:0.8rem">Play • Learn • Repeat</div>
            </div>
          </div>

          <div class="q-gutter-sm row items-center">
            <q-btn dense flat label="How it works" color="white" @click="$scrollTo('#how')" />
            <q-btn dense flat label="Play this week's game" color="primary" unelevated class="pulse-cta" @click="playWeek" />
          </div>
        </q-toolbar>
      </q-header>

      <!-- PAGE -->
      <q-page-container>
        <q-page class="container q-pb-xl q-pt-lg">

          <!-- HERO -->
          <section class="hero">
            <div class="row items-center q-col-gutter-md">
              <div class="col">
                <div class="row items-center q-gutter-sm q-mb-xs">
                  <div class="badge">Weekly Challenge</div>
                  <div class="muted q-ml-sm">New properties added every week • Come back frequently</div>
                </div>

                <h1 class="text-h3 q-mb-sm">The House Price Game — guess, learn, and sharpen your market sense</h1>
                <p class="muted q-mb-md">
                  Each week we select a fresh set of properties from across the area. Your job: place your best estimate of the market price.
                  The closer you are, the better your score — and every play gives instant feedback so you learn the signals professionals use.
                </p>

                <div class="row items-center q-gutter-sm q-mb-md">
                  <q-btn unelevated color="primary" label="Play this week's game" @click="playWeek" />
                  <q-btn flat color="white" label="Subscribe for weekly alerts" @click="subscribe()" />
                  <div class="learn-pill q-ml-md">This week: <span class="stat q-ml-xs">{{ properties.length }}</span> properties</div>
                </div>

                <!-- Weekly refresh info + countdown -->
                <div class="row items-center q-gutter-md">
                  <div class="muted">Week number:</div>
                  <div class="text-weight-bold" style="font-size:1.05rem">{{ currentWeek }}</div>
                  <div class="muted q-ml-lg">Next refresh in</div>
                  <div class="countdown q-ml-xs" id="countdown">{{ countdownStr }}</div>
                </div>
              </div>

              <!-- right column: quick learning benefits -->
              <div class="col-12 col-md-4 q-pt-md">
                <q-card flat bordered class="q-pa-md" style="background:linear-gradient(180deg, rgba(255,255,255,0.03), rgba(255,255,255,0.02))">
                  <div class="text-subtitle2 q-mb-sm">Why play? (3 big wins)</div>
                  <div class="row q-pb-sm">
                    <q-icon name="insights" class="q-mr-sm" />
                    <div>
                      <div class="text-caption muted">Signal recognition</div>
                      <div style="font-size:0.95rem">Learn what features, neighbourhoods and quirks move price.</div>
                    </div>
                  </div>
                  <div class="row q-pb-sm">
                    <q-icon name="bar_chart" class="q-mr-sm" />
                    <div>
                      <div class="text-caption muted">Instant feedback</div>
                      <div style="font-size:0.95rem">See the true sale price & comparables after each guess.</div>
                    </div>
                  </div>
                  <div class="row">
                    <q-icon name="school" class="q-mr-sm" />
                    <div>
                      <div class="text-caption muted">Practice makes expert</div>
                      <div style="font-size:0.95rem">Repeated play trains your valuation intuition — faster than passive reading.</div>
                    </div>
                  </div>
                </q-card>
              </div>
            </div>
          </section>

          <!-- HOW IT WORKS -->
          <section id="how" class="q-mt-lg">
            <div class="text-h6 q-mb-md">How the game works — quick & clear</div>
            <div class="row steps q-col-gutter-md">
              <div class="col-12 col-md-4">
                <q-card flat class="q-pa-md">
                  <div class="text-h6">1. Inspect</div>
                  <div class="muted q-mt-xs">We show photos, beds/baths, and a neighborhood hint — no price shown.</div>
                </q-card>
              </div>
              <div class="col-12 col-md-4">
                <q-card flat class="q-pa-md">
                  <div class="text-h6">2. Guess</div>
                  <div class="muted q-mt-xs">Make your estimate. Use your local knowledge — or trust a hunch.</div>
                </q-card>
              </div>
              <div class="col-12 col-md-4">
                <q-card flat class="q-pa-md">
                  <div class="text-h6">3. Learn</div>
                  <div class="muted q-mt-xs">We reveal the sale price and explain key comparables and price drivers.</div>
                </q-card>
              </div>
            </div>

            <div class="q-mt-md muted">Play multiple rounds to build confidence — you’ll start to notice patterns: what materials, floorplans or streets command premiums.</div>
          </section>

          <!-- PROPERTIES GRID -->
          <section class="q-mt-lg">
            <div class="row items-center justify-between q-mb-sm">
              <div class="text-h6">This week's properties</div>
              <div class="muted">Click Play on any card to try that property</div>
            </div>

            <div class="branded-game-grid">
              <q-card v-for="prop in properties" :key="prop.id" flat class="game-listing-card">
                <q-img :src="prop.image" :alt="prop.address" ratio="4/3" class="q-relative">
                  <div class="absolute-bottom text-subtitle2 q-pa-sm" style="background:linear-gradient(180deg, rgba(0,0,0,0.0), rgba(0,0,0,0.6)); width:100%">{{ prop.address }}</div>
                </q-img>

                <q-card-section>
                  <div class="row items-center justify-between">
                    <div>
                      <div class="text-subtitle1" style="font-weight:600">{{ prop.short }}</div>
                      <div class="muted q-mt-xs"><q-icon name="place" size="16" /> {{ prop.location }}</div>
                    </div>
                    <div class="row q-gutter-sm">
                      <q-btn dense flat round icon="share" @click="shareProperty(prop)" :aria-label="`Share ${prop.address}`" />
                      <q-btn dense unelevated color="primary" label="Play" @click="playFor(prop)" />
                    </div>
                  </div>
                </q-card-section>

                <q-separator />

                <q-card-section class="row items-center text-caption muted q-gutter-sm">
                  <div class="row items-center"><q-icon name="king_bed" class="q-mr-xs" /> {{ prop.beds }}</div>
                  <div class="row items-center q-ml-md"><q-icon name="bathtub" class="q-mr-xs" /> {{ prop.baths }}</div>
                  <div class="row items-center q-ml-md"><q-icon name="garage" class="q-mr-xs" /> {{ prop.garage }}</div>
                </q-card-section>
              </q-card>
            </div>
          </section>

          <!-- SOCIAL / SHARE CARD -->
          <section class="q-mt-xl">
            <q-card flat class="q-pa-md" style="background:linear-gradient(180deg, rgba(255,255,255,0.02), transparent)">
              <div class="row items-center justify-between">
                <div>
                  <div class="text-h6">Tell a friend — share the weekly challenge</div>
                  <div class="muted">Friendly rivalry helps learning. Invite friends, compare scores, and learn together.</div>
                </div>
                <div class="row">
                  <q-btn round icon="facebook" color="#1877F2" class="q-ma-xs" :href="'//www.facebook.com/sharer/sharer.php?u='+encodeURIComponent(baseUrl)" target="_blank" />
                  <q-btn round icon="twitter" color="#1DA1F2" class="q-ma-xs" :href="'https://x.com/intent/tweet?text='+encodeURIComponent('Try this weekly property price game: '+baseUrl)" target="_blank" />
                  <q-btn round icon="whatsapp" color="#25D366" class="q-ma-xs" :href="'https://wa.me/?text='+encodeURIComponent('Try this weekly property price game: '+baseUrl)" target="_blank" />
                </div>
              </div>
            </q-card>
          </section>

        </q-page>
      </q-page-container>

      <!-- FOOTER -->
      <q-footer elevated class="q-pa-md" style="background:linear-gradient(90deg,var(--brand), #3b57f0); color:#fff">
        <div class="container row items-center justify-between">
          <div class="muted">© <strong>HousePriceGuess</strong> — Learn the market, one guess at a time</div>
          <div class="row items-center">
            <a class="no-underline muted q-mr-md" href="/i/privacy">Privacy</a>
            <a class="no-underline muted q-mr-md" href="/i/terms-of-service">Terms</a>
            <div class="muted">Powered by <a class="no-underline" href="https://housepriceguess.com" target="_blank">HousePriceGuess</a></div>
          </div>
        </div>
      </q-footer>

    </q-layout>
  </div>

  <!-- Vue + Quasar (CDN) -->
  <script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.prod.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/quasar@2/dist/quasar.umd.prod.js"></script>

  <script>
    const { createApp, ref, computed, onMounted } = Vue;

    createApp({
      setup() {
        // Base URL used in share links
        const baseUrl = 'https://star.housepriceguess.com/';

        // sample properties (kept from your data)
        const properties = ref([
          { id: 1, address: '873 Essex Circle, Grayslake IL 60030', short: '873 Essex Circle', location: 'Grayslake, IL, USA', image: 'https://d25fhp1qfwqa2h.cloudfront.net/a2e81d3b02df7e9fb32d38593738d096,1757012718000_245_170?phid=16247539538', beds: 2, baths: 1.5, garage: 2 },
          { id: 2, address: '1544 Syracuse Drive, Grayslake IL 60030', short: '1544 Syracuse Drive', location: 'Grayslake, IL, USA', image: 'https://d25fhp1qfwqa2h.cloudfront.net/0b5db96906189de3d30731ee158ef340,1754589921000_auto_650?phid=15730487056', beds: 3, baths: 3.5, garage: 2 },
          { id: 3, address: '872 Tylerton Circle, Grayslake IL 60030', short: '872 Tylerton Circle', location: 'Grayslake, IL, USA', image: 'https://d25fhp1qfwqa2h.cloudfront.net/d0fea358ed43cd3b2923131aac0fbe5b,1756133421000_auto_650?phid=16052196327', beds: 3, baths: 3.5, garage: 2 },
          { id: 4, address: '18564 W Main Street, Grayslake IL 60030', short: '18564 W Main Street', location: 'Grayslake, IL, USA', image: 'https://d25fhp1qfwqa2h.cloudfront.net/086afaeff6f5f45008c9a71c5c82093c,1754690120000_auto_650?phid=15754747683', beds: 3, baths: 2, garage: 1 },
          { id: 5, address: '2814 Phillip Drive, Grayslake IL 60030', short: '2814 Phillip Drive', location: 'Grayslake, IL, USA', image: 'https://d25fhp1qfwqa2h.cloudfront.net/668f30d16ec1c9f6eb21f77536c9a8bd,1714507382000_auto_650?phid=9393976934', beds: 3, baths: 2.5, garage: 3 }
        ]);

        // Week number helper (ISO week)
        function getISOWeekNumber(dt) {
          const date = new Date(Date.UTC(dt.getFullYear(), dt.getMonth(), dt.getDate()));
          // Thursday in current week decides the year.
          date.setUTCDate(date.getUTCDate() + 4 - (date.getUTCDay()||7));
          const yearStart = new Date(Date.UTC(date.getUTCFullYear(),0,1));
          const weekNo = Math.ceil((((date - yearStart) / 86400000) + 1)/7);
          return weekNo;
        }

        const now = ref(new Date());
        const currentWeek = computed(() => getISOWeekNumber(now.value));

        // compute next weekly refresh: choose next Monday 00:00 local time
        function nextMonday(d=new Date()){
          const day = d.getDay(); // 0 Sun ... 1 Mon ...
          const daysUntil = (8 - day) % 7 || 7; // next Monday
          const nm = new Date(d);
          nm.setDate(d.getDate() + daysUntil);
          nm.setHours(0,0,0,0);
          return nm;
        }

        const nextRefresh = ref(nextMonday(now.value));
        const countdownStr = ref('');

        // countdown updater
        function updateCountdown(){
          const diff = nextRefresh.value - new Date();
          if (diff <= 0) {
            // recalc next refresh and week number
            nextRefresh.value = nextMonday(new Date());
            now.value = new Date();
          }
          const s = Math.max(0, Math.floor(diff/1000));
          const days = Math.floor(s / (3600*24));
          const hours = Math.floor((s % (3600*24)) / 3600);
          const minutes = Math.floor((s % 3600) / 60);
          const seconds = s % 60;
          countdownStr.value = `${days}d ${hours}h ${minutes}m ${seconds}s`;
        }

        // update every second (client-side UI)
        let timer = null;
        onMounted(() => {
          updateCountdown();
          timer = setInterval(() => {
            updateCountdown();
          }, 1000);
        });

        // actions
        function playFor(prop){
          Quasar.Dialog.create({
            title: 'Play this property',
            message: `Start a round for <strong>${prop.short}</strong>? You will be shown photos and details — then submit your price estimate.`,
            html: true,
            ok: 'Start',
            cancel: true
          }).onOk(() => {
            // In a real app you'd route to the property game: simulate with a notification
            Quasar.Notify.create({ type: 'positive', message: `Ready — opening game for ${prop.short}` });
          });
        }

        function shareProperty(prop){
          const text = `Guess the price of ${prop.short} — try it: ${baseUrl}`;
          // open basic share options
          const shareUrl = `https://x.com/intent/tweet?text=${encodeURIComponent(text)}`;
          window.open(shareUrl, '_blank', 'noopener');
        }

        function playWeek(){
          Quasar.Dialog.create({
            title: "Play this week's challenge",
            message: `This week's pack contains <strong>${properties.value.length}</strong> properties (week ${currentWeek.value}). Want to start the full weekly session?`,
            html: true,
            ok: 'Start weekly session',
            cancel: true
          }).onOk(() => {
            Quasar.Notify.create({ type: 'info', message: 'Starting the weekly session — have fun!' });
          });
        }

        function subscribe(){
          Quasar.Dialog.create({
            title: 'Subscribe for weekly alerts',
            message: 'Enter your email on the next screen (mock behaviour in this demo). In production this would sign you up for weekly property alerts.',
            html: true,
            ok: 'OK'
          });
        }

        // simple share base url for template usage
        return {
          properties,
          playFor,
          shareProperty,
          playWeek,
          subscribe,
          countdownStr,
          currentWeek,
          baseUrl
        };
      }
    })
    .use(Quasar, { config: {} })
    .mount('#q-app');
  </script>
</body>
</html>
