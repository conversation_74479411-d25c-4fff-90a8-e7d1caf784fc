{"name": "pwb-pro-quasar", "version": "0.0.1", "description": "Make Smarter Real Estate Choices", "productName": "HomesToCompare", "author": "<PERSON>", "type": "module", "private": true, "scripts": {"lint": "eslint --ext .js,.vue ./", "format": "prettier --write \"**/*.{js,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "vitest run", "test:watch": "vitest", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "dev": "ROOT_FOLDER_NAME=hpg-main quasar dev", "dev:ssr": "ROOT_FOLDER_NAME=hpg-main quasar dev -m ssr", "build": "quasar build", "generate-sitemap": "node generate-sitemap.js", "postinstall": "quasar prepare"}, "dependencies": {"@fawmi/vue-google-maps": "^0.9.79", "@googlemaps/js-api-loader": "^1.16.8", "@quasar/extras": "^1.16.4", "@quasar/quasar-ui-qcalendar": "^4.1.2", "@vue-leaflet/vue-leaflet": "^0.10.1", "ahoy.js": "^0.4.4", "apexcharts": "^4.4.0", "axios": "^1.2.1", "chart.js": "^4.4.7", "firebase": "^10.14.1", "lodash": "^4.17.21", "ol": "^10.3.1", "ol-contextmenu": "^5.5.0", "ol-ext": "^4.0.24", "pinia": "^2.0.11", "quasar": "^2.18.2", "vue": "^3.4.18", "vue-chartkick": "^1.1.0", "vue-currency-input": "^3.1.0", "vue-google-maps-community-fork": "^0.3.1", "vue-i18n": "^9.0.0", "vue-router": "^4.0.12", "vue3-apexcharts": "^1.8.0", "vue3-openlayers": "^11.2.1", "vuedraggable": "^4.1.0", "vuefire": "^3.2.0"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "^5.2.0", "@playwright/test": "^1.55.0", "@quasar/app-vite": "^2.3.0", "@vitejs/plugin-vue": "^6.0.0", "@vitest/ui": "3.2.4", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.2", "beasties": "^0.3.5", "eslint": "^8.57.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-vue": "^9.0.0", "jsdom": "^26.1.0", "leaflet": "^1.9.4", "postcss": "^8.4.14", "prettier": "^2.5.1", "vite": "^7.0.5", "vite-plugin-beasties": "^0.3.5", "vite-plugin-checker": "^0.8.0", "vitest": "^3.2.4"}, "engines": {"node": "^22 || ^20 || ^18", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}