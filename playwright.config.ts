import { defineConfig, devices } from '@playwright/test'

// E2E configuration for <PERSON><PERSON>. This expects your app to be running locally.
// You can start your server separately (e.g., quasar dev) before running tests.
export default defineConfig({
  testDir: 'tests/e2e',
  timeout: 60_000,
  expect: {
    timeout: 30_000,
  },
  // Base URL used by page.goto and page.locator URL resolution
  use: {
    baseURL: 'http://localhost:9100',
    trace: 'on-first-retry',
    video: 'retain-on-failure',
    screenshot: 'only-on-failure',
  },
  // Run on a single desktop browser by default; add more if needed
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],
})
