// Quick test to verify the Reddit route detection works correctly
import { useRealtyGame } from './src/concerns/realty-game/composables/useRealtyGame.js'

// Create a mock of the composable to test the isRedditRoute function
const { isRedditRoute } = useRealtyGame()

console.log('Testing Reddit route detection:')
console.log('rRedditPriceGameStart:', isRedditRoute('rRedditPriceGameStart')) // should be true
console.log('rRedditPriceGameProperty:', isRedditRoute('rRedditPriceGameProperty')) // should be true
console.log('rPriceGameStart:', isRedditRoute('rPriceGameStart')) // should be false
console.log('rPriceGameProperty:', isRedditRoute('rPriceGameProperty')) // should be false
console.log('undefined:', isRedditRoute(undefined)) // should be false
console.log('null:', isRedditRoute(null)) // should be false

// Test API URL construction logic
console.log('\nTesting API endpoint selection:')
console.log('Reddit route should use: /api_public/v4/realty_singular_game_summary')
console.log('Regular route should use: /api_public/v4/realty_game_summary')
